#!/bin/bash
DB_NAME="claudio2_pousada"
CONFIG_FILE="/home/<USER>/.my-pousada.cnf"
BACKUP_DIR="/home/<USER>/pousada.claudiosegura.com/autobackup"
LOG_FILE="$BACKUP_DIR/backup.log"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="backup_${DB_NAME}_${DATE}.sql"

{
    echo "[$DATE] Iniciando backup..."
    mysqldump --defaults-extra-file="$CONFIG_FILE" --no-tablespaces "$DB_NAME" > "$BACKUP_DIR/$BACKUP_FILE"
    DUMP_STATUS=$?

    if [ $DUMP_STATUS -eq 0 ]; then
        echo "[$DATE] Backup realizado com sucesso: $BACKUP_FILE"
        gzip "$BACKUP_DIR/$BACKUP_FILE"
        find "$BACKUP_DIR" -name "hmaxbackup_${DB_NAME}_*.sql.gz" -mtime +30 -delete
    else
        echo "[$DATE] ERRO ao realizar backup!"
    fi
} >> "$LOG_FILE" 2>&1