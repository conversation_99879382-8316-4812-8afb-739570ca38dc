-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.2
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Tempo de geração: 03/08/2025 às 20:49
-- V<PERSON><PERSON> do servidor: 8.0.42-cll-lve
-- Versão do PHP: 8.3.23

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- <PERSON><PERSON> de dados: `claudio2_pousada`
--

-- --------------------------------------------------------

--
-- Estrutura para tabela `backups_pousada`
--

CREATE TABLE `backups_pousada` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `nome_arquivo` varchar(255) NOT NULL,
  `data_criacao` datetime NOT NULL,
  `tamanho_arquivo` bigint DEFAULT NULL,
  `usuario_id` int NOT NULL,
  `tabelas_incluidas` text,
  `status` enum('sucesso','erro') DEFAULT 'sucesso'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `caixa_diario`
--

CREATE TABLE `caixa_diario` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `data_abertura` datetime NOT NULL,
  `data_fechamento` datetime DEFAULT NULL,
  `saldo_inicial` decimal(10,2) NOT NULL DEFAULT '0.00',
  `saldo_final` decimal(10,2) DEFAULT NULL,
  `usuario_abertura_id` int NOT NULL,
  `usuario_fechamento_id` int DEFAULT NULL,
  `status` enum('aberto','fechado') NOT NULL DEFAULT 'aberto',
  `observacao` text
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `categorias_financeiras`
--

CREATE TABLE `categorias_financeiras` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `nome` varchar(100) NOT NULL,
  `tipo` enum('receita','despesa') NOT NULL,
  `descricao` text,
  `cor` varchar(7) DEFAULT '#000000',
  `is_default` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `contratos`
--

CREATE TABLE `contratos` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `plano_id` int NOT NULL,
  `data_contratacao` date NOT NULL,
  `data_expiracao` date DEFAULT NULL,
  `status` enum('ativo','inativo') DEFAULT 'ativo'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `formas_pagamento`
--

CREATE TABLE `formas_pagamento` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `nome` varchar(100) NOT NULL,
  `descricao` text,
  `is_active` tinyint(1) DEFAULT '1',
  `afeta_caixa` tinyint(1) DEFAULT '0' COMMENT 'Indica se esta forma de pagamento afeta o caixa físico (1=sim, 0=não)'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `hospedes`
--

CREATE TABLE `hospedes` (
  `id` int NOT NULL,
  `nome` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `nasc` date DEFAULT NULL,
  `idade` tinyint DEFAULT NULL,
  `profissao` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `nacionalidade` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `sexo` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cpf` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `documento` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `tipo` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `expedidor` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `endereco` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `telefone` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cep` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `cidade` varchar(35) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `uf` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pais` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `pousada_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `lancamentos_financeiros`
--

CREATE TABLE `lancamentos_financeiros` (
  `id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `reserva_id` int DEFAULT NULL,
  `tipo` enum('receita','despesa') NOT NULL,
  `categoria_id` int NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `data_lancamento` date NOT NULL,
  `data_vencimento` date NOT NULL,
  `data_pagamento` date DEFAULT NULL,
  `status` enum('pendente','pago','cancelado') NOT NULL DEFAULT 'pendente',
  `forma_pagamento` varchar(50) DEFAULT NULL,
  `observacao` text,
  `usuario_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `movimentacoes_caixa`
--

CREATE TABLE `movimentacoes_caixa` (
  `id` int NOT NULL,
  `caixa_id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `lancamento_id` int DEFAULT NULL,
  `tipo` enum('entrada','saida','suprimento','sangria') NOT NULL,
  `valor` decimal(10,2) NOT NULL,
  `descricao` varchar(255) NOT NULL,
  `data_hora` datetime NOT NULL,
  `usuario_id` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `planos`
--

CREATE TABLE `planos` (
  `id` int NOT NULL,
  `nome` varchar(50) NOT NULL,
  `descricao` text,
  `preco` decimal(10,2) NOT NULL,
  `limite_usuarios` int NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `pousadas`
--

CREATE TABLE `pousadas` (
  `id` int NOT NULL,
  `nome` varchar(100) NOT NULL,
  `cnpj` varchar(14) NOT NULL,
  `rua` varchar(100) NOT NULL,
  `numero` varchar(10) NOT NULL,
  `complemento` varchar(50) DEFAULT NULL,
  `bairro` varchar(50) NOT NULL,
  `cidade` varchar(50) NOT NULL,
  `estado` varchar(2) NOT NULL,
  `cep` varchar(10) NOT NULL,
  `pais` varchar(50) NOT NULL,
  `telefone` varchar(20) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `logotipo` varchar(255) DEFAULT NULL,
  `data_cadastro` datetime DEFAULT CURRENT_TIMESTAMP,
  `cor_primaria` varchar(7) DEFAULT '#28a745' COMMENT 'Cor primária da pousada em formato HEX',
  `cor_clara` varchar(7) DEFAULT '#a8e6c1' COMMENT 'Cor clara gerada automaticamente',
  `cor_escura` varchar(7) DEFAULT '#1e7e34' COMMENT 'Cor escura gerada automaticamente',
  `cor_rgb` varchar(20) DEFAULT '40, 167, 69' COMMENT 'Valores RGB da cor primária'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `reservas`
--

CREATE TABLE `reservas` (
  `id` int NOT NULL,
  `hospede_id` int NOT NULL,
  `pousada_id` int NOT NULL,
  `uh` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `numacomp` tinyint DEFAULT '0',
  `dataentrada` date DEFAULT NULL,
  `horaentrada` time DEFAULT NULL,
  `datasaida` date DEFAULT NULL,
  `horasaida` time DEFAULT NULL,
  `vemde` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `vaipara` varchar(50) CHARACTER SET utf8mb3 COLLATE utf8mb3_general_ci DEFAULT NULL,
  `motivo` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `transporte` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
  `acompanhantes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
  `valor` decimal(10,2) DEFAULT NULL,
  `observacao` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `sql_query_history`
--

CREATE TABLE `sql_query_history` (
  `id` int NOT NULL,
  `user_id` int NOT NULL,
  `pousada_id` int NOT NULL DEFAULT '0',
  `query_text` text NOT NULL,
  `executed_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `execution_time` decimal(10,4) DEFAULT NULL,
  `affected_rows` int DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

-- --------------------------------------------------------

--
-- Estrutura para tabela `usuarios`
--

CREATE TABLE `usuarios` (
  `id` int NOT NULL,
  `nome` varchar(50) NOT NULL,
  `senha` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `pousada_id` int NOT NULL,
  `is_admin` tinyint(1) DEFAULT '0'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- Índices para tabelas despejadas
--

--
-- Índices de tabela `backups_pousada`
--
ALTER TABLE `backups_pousada`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_pousada_data` (`pousada_id`,`data_criacao` DESC);

--
-- Índices de tabela `caixa_diario`
--
ALTER TABLE `caixa_diario`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`),
  ADD KEY `usuario_abertura_id` (`usuario_abertura_id`),
  ADD KEY `usuario_fechamento_id` (`usuario_fechamento_id`);

--
-- Índices de tabela `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- Índices de tabela `contratos`
--
ALTER TABLE `contratos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`),
  ADD KEY `plano_id` (`plano_id`);

--
-- Índices de tabela `formas_pagamento`
--
ALTER TABLE `formas_pagamento`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- Índices de tabela `hospedes`
--
ALTER TABLE `hospedes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- Índices de tabela `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  ADD PRIMARY KEY (`id`),
  ADD KEY `pousada_id` (`pousada_id`),
  ADD KEY `reserva_id` (`reserva_id`),
  ADD KEY `categoria_id` (`categoria_id`),
  ADD KEY `usuario_id` (`usuario_id`);

--
-- Índices de tabela `movimentacoes_caixa`
--
ALTER TABLE `movimentacoes_caixa`
  ADD PRIMARY KEY (`id`),
  ADD KEY `caixa_id` (`caixa_id`),
  ADD KEY `lancamento_id` (`lancamento_id`),
  ADD KEY `usuario_id` (`usuario_id`),
  ADD KEY `idx_pousada_id` (`pousada_id`);

--
-- Índices de tabela `planos`
--
ALTER TABLE `planos`
  ADD PRIMARY KEY (`id`);

--
-- Índices de tabela `pousadas`
--
ALTER TABLE `pousadas`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `cnpj` (`cnpj`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Índices de tabela `reservas`
--
ALTER TABLE `reservas`
  ADD PRIMARY KEY (`id`),
  ADD KEY `hospede_id` (`hospede_id`);

--
-- Índices de tabela `sql_query_history`
--
ALTER TABLE `sql_query_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_pousada` (`user_id`,`pousada_id`),
  ADD KEY `idx_executed_at` (`executed_at` DESC),
  ADD KEY `idx_pousada_executed` (`pousada_id`,`executed_at` DESC);

--
-- Índices de tabela `usuarios`
--
ALTER TABLE `usuarios`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `pousada_id` (`pousada_id`);

--
-- AUTO_INCREMENT para tabelas despejadas
--

--
-- AUTO_INCREMENT de tabela `backups_pousada`
--
ALTER TABLE `backups_pousada`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `caixa_diario`
--
ALTER TABLE `caixa_diario`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `contratos`
--
ALTER TABLE `contratos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `formas_pagamento`
--
ALTER TABLE `formas_pagamento`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `hospedes`
--
ALTER TABLE `hospedes`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `movimentacoes_caixa`
--
ALTER TABLE `movimentacoes_caixa`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `planos`
--
ALTER TABLE `planos`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `pousadas`
--
ALTER TABLE `pousadas`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `reservas`
--
ALTER TABLE `reservas`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `sql_query_history`
--
ALTER TABLE `sql_query_history`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT de tabela `usuarios`
--
ALTER TABLE `usuarios`
  MODIFY `id` int NOT NULL AUTO_INCREMENT;

--
-- Restrições para tabelas despejadas
--

--
-- Restrições para tabelas `caixa_diario`
--
ALTER TABLE `caixa_diario`
  ADD CONSTRAINT `caixa_diario_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`),
  ADD CONSTRAINT `caixa_diario_ibfk_2` FOREIGN KEY (`usuario_abertura_id`) REFERENCES `usuarios` (`id`),
  ADD CONSTRAINT `caixa_diario_ibfk_3` FOREIGN KEY (`usuario_fechamento_id`) REFERENCES `usuarios` (`id`);

--
-- Restrições para tabelas `categorias_financeiras`
--
ALTER TABLE `categorias_financeiras`
  ADD CONSTRAINT `categorias_financeiras_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `contratos`
--
ALTER TABLE `contratos`
  ADD CONSTRAINT `contratos_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`),
  ADD CONSTRAINT `contratos_ibfk_2` FOREIGN KEY (`plano_id`) REFERENCES `planos` (`id`);

--
-- Restrições para tabelas `formas_pagamento`
--
ALTER TABLE `formas_pagamento`
  ADD CONSTRAINT `formas_pagamento_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `hospedes`
--
ALTER TABLE `hospedes`
  ADD CONSTRAINT `hospedes_ibfk_2` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `lancamentos_financeiros`
--
ALTER TABLE `lancamentos_financeiros`
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`),
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_2` FOREIGN KEY (`reserva_id`) REFERENCES `reservas` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_3` FOREIGN KEY (`categoria_id`) REFERENCES `categorias_financeiras` (`id`),
  ADD CONSTRAINT `lancamentos_financeiros_ibfk_4` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`);

--
-- Restrições para tabelas `movimentacoes_caixa`
--
ALTER TABLE `movimentacoes_caixa`
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_1` FOREIGN KEY (`caixa_id`) REFERENCES `caixa_diario` (`id`),
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_2` FOREIGN KEY (`lancamento_id`) REFERENCES `lancamentos_financeiros` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_3` FOREIGN KEY (`usuario_id`) REFERENCES `usuarios` (`id`),
  ADD CONSTRAINT `movimentacoes_caixa_ibfk_4` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);

--
-- Restrições para tabelas `reservas`
--
ALTER TABLE `reservas`
  ADD CONSTRAINT `reservas_ibfk_1` FOREIGN KEY (`hospede_id`) REFERENCES `hospedes` (`id`);

--
-- Restrições para tabelas `usuarios`
--
ALTER TABLE `usuarios`
  ADD CONSTRAINT `usuarios_ibfk_1` FOREIGN KEY (`pousada_id`) REFERENCES `pousadas` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
