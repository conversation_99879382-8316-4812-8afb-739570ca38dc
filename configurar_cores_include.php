<?php
// Versão include do configurar_cores.php para ser usado dentro do index.php

// Headers para evitar cache
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Verificar se usuário está logado
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
    header("Location: login.php");
    exit();
}

// Não precisamos mais de arquivo CSS - usamos apenas variáveis da sessão

// Função para converter HEX para RGB
function hexToRgb($hex) {
    $hex = ltrim($hex, '#');
    return [
        'r' => hexdec(substr($hex, 0, 2)),
        'g' => hexdec(substr($hex, 2, 2)),
        'b' => hexdec(substr($hex, 4, 2))
    ];
}

// Função para converter RGB para HSL
function rgbToHsl($r, $g, $b) {
    $r /= 255;
    $g /= 255;
    $b /= 255;
    
    $max = max($r, $g, $b);
    $min = min($r, $g, $b);
    $h = $s = $l = ($max + $min) / 2;
    
    if ($max == $min) {
        $h = $s = 0;
    } else {
        $d = $max - $min;
        $s = $l > 0.5 ? $d / (2 - $max - $min) : $d / ($max + $min);
        
        switch ($max) {
            case $r: $h = ($g - $b) / $d + ($g < $b ? 6 : 0); break;
            case $g: $h = ($b - $r) / $d + 2; break;
            case $b: $h = ($r - $g) / $d + 4; break;
        }
        $h /= 6;
    }
    
    return [$h, $s, $l];
}

// Função para converter HSL para HEX
function hslToHex($h, $s, $l) {
    $hue2rgb = function($p, $q, $t) {
        if ($t < 0) $t += 1;
        if ($t > 1) $t -= 1;
        if ($t < 1/6) return $p + ($q - $p) * 6 * $t;
        if ($t < 1/2) return $q;
        if ($t < 2/3) return $p + ($q - $p) * (2/3 - $t) * 6;
        return $p;
    };
    
    if ($s == 0) {
        $r = $g = $b = $l;
    } else {
        $q = $l < 0.5 ? $l * (1 + $s) : $l + $s - $l * $s;
        $p = 2 * $l - $q;
        $r = $hue2rgb($p, $q, $h + 1/3);
        $g = $hue2rgb($p, $q, $h);
        $b = $hue2rgb($p, $q, $h - 1/3);
    }
    
    return sprintf("#%02x%02x%02x", 
        round($r * 255), 
        round($g * 255), 
        round($b * 255)
    );
}

// Função para gerar variações de cor
function gerarVariacoesCor($corPrimaria) {
    $rgb = hexToRgb($corPrimaria);
    $hsl = rgbToHsl($rgb['r'], $rgb['g'], $rgb['b']);
    
    // Gerar cor clara (aumentar luminosidade)
    $corClara = hslToHex($hsl[0], max(0.2, $hsl[1] * 0.3), min(0.95, $hsl[2] + 0.4));
    
    // Gerar cor escura (diminuir luminosidade)
    $corEscura = hslToHex($hsl[0], min(1, $hsl[1] * 1.2), max(0.1, $hsl[2] - 0.3));
    
    return [
        'primaria' => $corPrimaria,
        'clara' => $corClara,
        'escura' => $corEscura,
        'rgb' => $rgb['r'] . ', ' . $rgb['g'] . ', ' . $rgb['b']
    ];
}

// Função para ler a cor atual da sessão
function lerCorAtual() {
    return $_SESSION['pousada_cores']['primaria'] ?? '#28a745';
}

// Função para salvar as cores no banco de dados
function salvarCoresDatabase($pousadaId, $tema) {
    global $conn;

    $sql = "UPDATE pousadas SET cor_primaria = ?, cor_clara = ?, cor_escura = ?, cor_rgb = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);

    if ($stmt === false) {
        return false;
    }

    $stmt->bind_param('ssssi', $tema['primaria'], $tema['clara'], $tema['escura'], $tema['rgb'], $pousadaId);
    $result = $stmt->execute();
    $stmt->close();

    return $result;
}

// Função para salvar as cores no CSS
function salvarCoresCSS($arquivo, $tema) {
    $css_content = "/* Cores Personalizadas - Gerado automaticamente */\n";
    $css_content .= "/* Cor primária: {$tema['primaria']} */\n";
    $css_content .= "/* Gerado em: " . date('Y-m-d H:i:s') . " */\n\n";
    
    $css_content .= "/* Background do body */\n";
    $css_content .= "body {\n";
    $css_content .= "    background-color: rgba({$tema['rgb']}, 0.1) !important;\n";
    $css_content .= "    font-family: Arial, sans-serif;\n";
    $css_content .= "}\n\n";
    
    $css_content .= "/* Navbar com gradiente */\n";
    $css_content .= ".navbar.navbar-expand-lg.navbar-light {\n";
    $css_content .= "    background: linear-gradient(135deg, {$tema['primaria']} 0%, {$tema['clara']} 100%) !important;\n";
    $css_content .= "    border-bottom: none !important;\n";
    $css_content .= "}\n\n";
    
    $css_content .= "/* Links da navbar */\n";
    $css_content .= ".navbar .nav-link {\n";
    $css_content .= "    color: white !important;\n";
    $css_content .= "}\n\n";
    
    $css_content .= ".navbar .nav-link:hover {\n";
    $css_content .= "    color: {$tema['escura']} !important;\n";
    $css_content .= "    background-color: rgba(255, 255, 255, 0.1) !important;\n";
    $css_content .= "    border-radius: 4px;\n";
    $css_content .= "}\n\n";
    
    $css_content .= "/* Botões */\n";
    $css_content .= ".form-group button,\n";
    $css_content .= ".btn-success {\n";
    $css_content .= "    background-color: {$tema['primaria']} !important;\n";
    $css_content .= "    border-color: {$tema['primaria']} !important;\n";
    $css_content .= "}\n\n";
    
    $css_content .= ".form-group button:hover,\n";
    $css_content .= ".btn-success:hover {\n";
    $css_content .= "    background-color: {$tema['escura']} !important;\n";
    $css_content .= "    border-color: {$tema['escura']} !important;\n";
    $css_content .= "}\n\n";
    
    $css_content .= "/* Células livres */\n";
    $css_content .= ".livre {\n";
    $css_content .= "    background-color: {$tema['clara']} !important;\n";
    $css_content .= "}\n\n";
    
    $css_content .= "/* Alertas */\n";
    $css_content .= ".alert-success {\n";
    $css_content .= "    background-color: {$tema['clara']} !important;\n";
    $css_content .= "    border-color: {$tema['primaria']} !important;\n";
    $css_content .= "    color: {$tema['escura']} !important;\n";
    $css_content .= "}\n";
    
    return file_put_contents($arquivo, $css_content) !== false;
}

// Processar salvamento das configurações
if ($_POST && isset($_POST['cor_primaria'])) {
    $cor_primaria = $_POST['cor_primaria'];
    $pousadaId = $_SESSION['user_pousada_id'];

    // Validar formato da cor
    if (preg_match('/^#[0-9a-fA-F]{6}$/', $cor_primaria)) {
        // Gerar variações da cor
        $tema = gerarVariacoesCor($cor_primaria);

        // Salvar no banco de dados
        if (salvarCoresDatabase($pousadaId, $tema)) {
            // Atualizar sessão com novas cores
            $_SESSION['pousada_cores'] = $tema;

            // Debug: verificar se cores foram salvas na sessão
            error_log("Cores salvas na sessão: " . json_encode($_SESSION['pousada_cores']));

            // Aplicar CSS imediatamente após salvar
            echo "<script>
                // Aplicar cores da sessão recém salvas
                const root = document.documentElement;
                root.style.setProperty('--cor-primaria', '{$tema['primaria']}');
                root.style.setProperty('--cor-clara', '{$tema['clara']}');
                root.style.setProperty('--cor-escura', '{$tema['escura']}');
                root.style.setProperty('--cor-rgb', '{$tema['rgb']}');
                console.log('Cores aplicadas após salvar:', " . json_encode($tema) . ");

                // Redirecionar após aplicar as cores
                setTimeout(function() {
                    window.location.href = 'index.php';
                }, 100);
            </script>";
            exit();
        } else {
            $mensagem_erro = "Erro ao salvar as configurações no banco de dados.";
        }
    } else {
        $mensagem_erro = "Formato de cor inválido.";
    }
}

// Não precisamos mais verificar ?success pois redirecionamos para home

// Não precisamos gerar arquivos CSS - as cores vêm da sessão

// Ler cor atual da sessão
$cor_atual = lerCorAtual();
?>

<!-- CSS específico para esta página -->
<style>
    .color-swatch {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        border: 3px solid #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        cursor: pointer;
        margin: 5px;
        transition: all 0.2s ease;
        position: relative;
    }
    
    .color-swatch:hover {
        transform: scale(1.1);
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    .color-swatch.selected {
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0,123,255,0.25);
        transform: scale(1.05);
    }
    
    .color-swatch.selected::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
        font-size: 16px;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    }
    
    .preset-colors {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
        padding: 15px;
        background-color: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #dee2e6;
    }
    
    .form-control-color {
        width: 100%;
        height: 50px;
        border-radius: 8px;
        border: 2px solid #dee2e6;
        cursor: pointer;
    }
</style>

<!-- Conteúdo da página -->
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-lg-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-palette"></i> Configurar Cores do Sistema
                    </h4>
                    
                </div>
                <div class="card-body">
                    <!-- Mensagens de erro apenas -->
                    <?php if (isset($mensagem_erro)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle"></i> <?php echo $mensagem_erro; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" id="formCores">
                        <div class="row">
                            <div class="col-12">
                                <h5>Cor pré-definida</h5>
                                
                                <!-- Cores Predefinidas -->
                                <div class="mb-3">
                                    
                                    <div class="preset-colors">
                                        <div class="color-swatch" data-color="#28a745" style="background-color: #28a745;" title="Verde (Padrão)"></div>
                                        <div class="color-swatch" data-color="#007bff" style="background-color: #007bff;" title="Azul"></div>
                                        <div class="color-swatch" data-color="#6f42c1" style="background-color: #6f42c1;" title="Roxo"></div>
                                        <div class="color-swatch" data-color="#e83e8c" style="background-color: #e83e8c;" title="Rosa"></div>
                                        <div class="color-swatch" data-color="#fd7e14" style="background-color: #fd7e14;" title="Laranja"></div>
                                        <div class="color-swatch" data-color="#20c997" style="background-color: #20c997;" title="Teal"></div>
                                        <div class="color-swatch" data-color="#6c757d" style="background-color: #6c757d;" title="Cinza"></div>
                                        <div class="color-swatch" data-color="#dc3545" style="background-color: #dc3545;" title="Vermelho"></div>
                                    </div>
                                </div>

                                <!-- Seletor de Cor Personalizada -->
                                <div class="mb-3">
                                    <label for="corPersonalizada" class="form-label"><h5>Cor personalizada:</h5></label>
                                    <input type="color" class="form-control form-control-color" id="corPersonalizada" value="<?php echo $cor_atual; ?>">
                                </div>

                                <input type="hidden" name="cor_primaria" id="corPrimaria" value="<?php echo $cor_atual; ?>">
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex justify-content-between btn-group-actions">
                                   <!-- Botão para voltar ao Home -->
                                    <div class="text-center">
                                        <a href="index.php" class="btn btn-secondary">
                                            <i class="fas fa-home me-2"></i>
                                            Home
                                        </a>
                                    </div>

                                    <button type="submit" class="btn btn-success" id="btnSalvar">
                                        <i class="bi bi-check-circle"></i> Salvar Configurações
                                    </button>


                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // JavaScript para interação (sem conflitos)
    document.addEventListener('DOMContentLoaded', function() {
        const swatches = document.querySelectorAll('.color-swatch');
        const corPersonalizada = document.getElementById('corPersonalizada');
        const corPrimaria = document.getElementById('corPrimaria');
        
        // Função para gerar variações de cor (JavaScript)
        function gerarVariacoesCor(corPrimaria) {
            // Converter HEX para RGB
            const hex = corPrimaria.replace('#', '');
            const r = parseInt(hex.substr(0, 2), 16);
            const g = parseInt(hex.substr(2, 2), 16);
            const b = parseInt(hex.substr(4, 2), 16);

            // Gerar cor clara (mais clara)
            const rClara = Math.min(255, Math.round(r + (255 - r) * 0.6));
            const gClara = Math.min(255, Math.round(g + (255 - g) * 0.6));
            const bClara = Math.min(255, Math.round(b + (255 - b) * 0.6));
            const corClara = `#${rClara.toString(16).padStart(2, '0')}${gClara.toString(16).padStart(2, '0')}${bClara.toString(16).padStart(2, '0')}`;

            // Gerar cor escura (mais escura)
            const rEscura = Math.round(r * 0.7);
            const gEscura = Math.round(g * 0.7);
            const bEscura = Math.round(b * 0.7);
            const corEscura = `#${rEscura.toString(16).padStart(2, '0')}${gEscura.toString(16).padStart(2, '0')}${bEscura.toString(16).padStart(2, '0')}`;

            return {
                primaria: corPrimaria,
                clara: corClara,
                escura: corEscura,
                rgb: `${r}, ${g}, ${b}`
            };
        }

        // Função para aplicar preview das cores
        function aplicarPreviewCores(cor) {
            const tema = gerarVariacoesCor(cor);
            const root = document.documentElement;

            root.style.setProperty('--cor-primaria', tema.primaria);
            root.style.setProperty('--cor-clara', tema.clara);
            root.style.setProperty('--cor-escura', tema.escura);
            root.style.setProperty('--cor-rgb', tema.rgb);
        }

        // Função para atualizar seleção
        function atualizarSelecao(cor) {
            corPersonalizada.value = cor;
            corPrimaria.value = cor;

            // Atualizar seleção visual
            swatches.forEach(s => s.classList.remove('selected'));
            const selectedSwatch = document.querySelector(`[data-color="${cor}"]`);
            if (selectedSwatch) {
                selectedSwatch.classList.add('selected');
            }

            // Aplicar preview instantâneo
            aplicarPreviewCores(cor);
        }
        
        // Event listeners para swatches
        swatches.forEach(swatch => {
            swatch.addEventListener('click', function() {
                const cor = this.getAttribute('data-color');
                atualizarSelecao(cor);
            });
        });
        
        // Event listener para seletor personalizado
        if (corPersonalizada) {
            corPersonalizada.addEventListener('input', function() {
                atualizarSelecao(this.value);
            });
        }
        
        // Aplicar cor inicial (sem preview para não sobrescrever cores da sessão)
        const corInicial = '<?php echo $cor_atual; ?>';
        corPersonalizada.value = corInicial;
        corPrimaria.value = corInicial;

        // Atualizar seleção visual apenas
        swatches.forEach(s => s.classList.remove('selected'));
        const selectedSwatch = document.querySelector(`[data-color="${corInicial}"]`);
        if (selectedSwatch) {
            selectedSwatch.classList.add('selected');
        }

        // Aplicar cores da sessão no carregamento da página
        <?php if (isset($_SESSION['pousada_cores'])): ?>
        const coresSessao = {
            primaria: '<?php echo $_SESSION['pousada_cores']['primaria']; ?>',
            clara: '<?php echo $_SESSION['pousada_cores']['clara']; ?>',
            escura: '<?php echo $_SESSION['pousada_cores']['escura']; ?>',
            rgb: '<?php echo $_SESSION['pousada_cores']['rgb']; ?>'
        };

        const root = document.documentElement;
        root.style.setProperty('--cor-primaria', coresSessao.primaria);
        root.style.setProperty('--cor-clara', coresSessao.clara);
        root.style.setProperty('--cor-escura', coresSessao.escura);
        root.style.setProperty('--cor-rgb', coresSessao.rgb);

        console.log('Cores da sessão aplicadas:', coresSessao);
        <?php endif; ?>
    });

</script>
