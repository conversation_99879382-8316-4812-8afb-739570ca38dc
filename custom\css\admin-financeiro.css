/* ========================================
   ESTILOS ADMINISTRATIVOS E FINANCEIROS
   Consolidação de estilos inline dos arquivos PHP
   ======================================== */

/* ========================================
   ESTILOS PARA ADMIN SQL
   ======================================== */

/* Botões de tabela ativos */
.list-group-item.active .table-btn {
    color: white !important;
    text-decoration: none !important;
    background-color: transparent !important;
}

/* Hover nos campos da tabela */
.field-item:hover {
    background-color: #f8f9fa;
}

/* ========================================
   ESTILOS PARA ADMIN POUSADAS
   ======================================== */

/* Feedback do CNPJ */
.cnpj-feedback {
    margin-top: 5px;
}

/* ========================================
   ESTILOS PARA HOSPEDES
   ======================================== */

/* Ajuste de altura para campos de busca */
.hospedes-procurar select,
.hospedes-procurar input[type="text"] {
    height: calc(1.5em + .75rem + 2px);
}

/* ========================================
   ESTILOS PARA CATEGORIAS FINANCEIRAS
   ======================================== */

.categoria-item {
    border-left: 5px solid #ccc;
    padding: 10px 15px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.categoria-item:hover {
    background-color: #e9ecef;
}

.categoria-cor {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 10px;
    border: 1px solid rgba(0, 0, 0, 0.2);
}

.categoria-default {
    font-style: italic;
    color: #6c757d;
}

/* ========================================
   ESTILOS PARA FORMAS DE PAGAMENTO
   ======================================== */

.forma-pagamento-item {
    padding: 15px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-left: 5px solid #0d6efd;
}

.forma-pagamento-item:hover {
    background-color: #e9ecef;
}

.forma-pagamento-inativa {
    opacity: 0.6;
    border-left-color: #6c757d;
}

.forma-pagamento-acoes {
    white-space: nowrap;
}

.badge-status {
    font-size: 0.8em;
    padding: 5px 8px;
    border-radius: 10px;
    margin-left: 10px;
}

/* ========================================
   ESTILOS PARA LANÇAMENTOS FINANCEIROS
   ======================================== */

.lancamento-card {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 5px;
    background-color: #f8f9fa;
    border-left: 5px solid #0d6efd;
}

.lancamento-receita {
    border-left-color: #28a745;
}

.lancamento-despesa {
    border-left-color: #dc3545;
}

.lancamento-pendente {
    background-color: #fff3cd;
}

.lancamento-pago {
    background-color: #d1e7dd;
}

.lancamento-cancelado {
    background-color: #f8d7da;
    text-decoration: line-through;
}

.categoria-badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    color: #fff;
}

.filtros-container {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.resumo-container {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.resumo-item {
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 10px;
}

.resumo-receitas {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 5px solid #28a745;
}

.resumo-despesas {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 5px solid #dc3545;
}

.resumo-saldo {
    background-color: rgba(13, 110, 253, 0.1);
    border-left: 5px solid #0d6efd;
}

.resumo-pendente {
    background-color: rgba(255, 193, 7, 0.1);
    border-left: 5px solid #ffc107;
}

.resumo-pago {
    background-color: rgba(25, 135, 84, 0.1);
    border-left: 5px solid #198754;
}

.valor-receita {
    color: #28a745;
    font-weight: bold;
}

.valor-despesa {
    color: #dc3545;
    font-weight: bold;
}

.valor-saldo-positivo {
    color: #28a745;
    font-weight: bold;
}

.valor-saldo-negativo {
    color: #dc3545;
    font-weight: bold;
}

.btn-filtrar {
    margin-top: 32px;
}

/* ========================================
   ESTILOS PARA CAIXA DIÁRIO
   ======================================== */

.card-caixa {
    border-left: 5px solid #0d6efd;
}

.card-caixa-fechado {
    border-left-color: #dc3545;
}

.movimentacao-entrada {
    border-left: 4px solid #28a745;
    padding-left: 10px;
    margin-bottom: 10px;
}

.movimentacao-saida {
    border-left: 4px solid #dc3545;
    padding-left: 10px;
    margin-bottom: 10px;
}

.movimentacao-suprimento {
    border-left: 4px solid #0d6efd;
    padding-left: 10px;
    margin-bottom: 10px;
}

.movimentacao-sangria {
    border-left: 4px solid #ffc107;
    padding-left: 10px;
    margin-bottom: 10px;
}

.valor-positivo {
    color: #28a745;
    font-weight: bold;
}

.valor-negativo {
    color: #dc3545;
    font-weight: bold;
}

.resumo-item {
    display: flex;
    justify-content: space-between;
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.resumo-item:last-child {
    border-bottom: none;
}

.resumo-total {
    font-weight: bold;
    font-size: 1.1em;
    border-top: 2px solid #333;
    padding-top: 10px;
    margin-top: 10px;
}

/* ========================================
   ESTILOS PARA IMPRESSÃO DE RELATÓRIOS
   ======================================== */

@media print {
    .no-print {
        display: none !important;
    }

    body {
        padding: 20px;
    }

    .container-fluid {
        width: 100%;
        padding: 0;
    }

    .card {
        border: none;
        margin-bottom: 30px;
    }

    .card-header {
        background-color: #f8f9fa !important;
        color: #000 !important;
        border-bottom: 1px solid #ddd;
    }

    h2,
    h3,
    h4,
    h5 {
        color: #000 !important;
    }

    .text-success {
        color: #28a745 !important;
    }

    .text-danger {
        color: #dc3545 !important;
    }

    .text-primary {
        color: #007bff !important;
    }

    .text-warning {
        color: #ffc107 !important;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    table th,
    table td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    table th {
        background-color: #f8f9fa !important;
    }

    .badge {
        border: 1px solid #000;
    }
}

/* ========================================
   ESTILOS PARA TESTES E DEMOS
   ======================================== */

.demo-cell {
    width: 60px;
    height: 40px;
    border: 1px solid #dee2e6;
    text-align: center;
    vertical-align: middle;
    padding: 6px 4px;
    margin: 5px;
    display: inline-block;
}

.demo-container {
    margin: 20px 0;
}

/* ========================================
   ESTILOS PARA IMPRESSÃO
   ======================================== */

.print-logo {
    max-width: 200px;
    max-height: 100px;
}

/* ========================================
   RESPONSIVIDADE
   ======================================== */

@media (max-width: 768px) {

    .categoria-item,
    .forma-pagamento-item,
    .lancamento-card {
        padding: 10px;
        margin-bottom: 8px;
    }

    .categoria-cor {
        width: 16px;
        height: 16px;
        margin-right: 8px;
    }

    .forma-pagamento-acoes {
        flex-direction: column;
        gap: 5px;
    }
}