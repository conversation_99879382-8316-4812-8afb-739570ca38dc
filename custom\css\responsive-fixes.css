/* ========================================
   RESPONSIVE FIXES - MOBILE & DESKTOP
   ======================================== */

/* ========================================
   VARIÁVEIS CSS PARA FACILITAR AJUSTES
   ======================================== */
:root {
  /* Sistema de Cores Personalizáveis */
  --cor-primaria: #28a745;
  --cor-primaria-clara: #d4edda;
  --cor-primaria-escura: #155724;
  --cor-primaria-rgb: 40, 167, 69;

  /* Cores de destaque (baseadas na cor primária) */
  --destaque-data-bg: #b3d9ff;
  --destaque-data-color: #0056b3;
  --destaque-data-border: #80c7ff;

  /* Tamanhos de fonte */
  --font-size-mobile: 14px;
  --font-size-tablet: 15px;
  --font-size-desktop: 16px;

  /* Espaçamentos */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 24px;
}

/* ========================================
   APLICAÇÃO DO TEMA PERSONALIZADO
   ======================================== */

/* Sobrescrever cores do Bootstrap com as personalizadas */
:root {
  --bs-success: var(--cor-primaria);
  --bs-success-rgb: var(--cor-primaria-rgb);
  --bs-success-bg-subtle: var(--cor-primaria-clara);
  --bs-success-text-emphasis: var(--cor-primaria-escura);
}

/* Aplicar cor primária ao body quando necessário */
body.tema-personalizado {
  --bs-primary: var(--cor-primaria);
  --bs-primary-rgb: var(--cor-primaria-rgb);
}

/* Gradiente personalizado para áreas de destaque */
.bg-gradient-personalizado {
  background: linear-gradient(135deg, var(--cor-primaria) 0%, var(--cor-primaria-clara) 100%) !important;
}

/* Navbar com tema personalizado */
.tema-personalizado .navbar-light,
.tema-personalizado .navbar-light.bg-light {
  background: linear-gradient(135deg, var(--cor-primaria) 0%, var(--cor-primaria-clara) 100%) !important;
  border-bottom: 2px solid var(--cor-primaria-escura) !important;
}

.tema-personalizado .navbar-light .navbar-nav .nav-link {
  color: white !important;
}

.tema-personalizado .navbar-light .navbar-nav .nav-link:hover {
  color: var(--cor-primaria-escura) !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px;
}

/* Botões com tema personalizado */
.btn-success.tema-personalizado {
  background-color: var(--cor-primaria) !important;
  border-color: var(--cor-primaria) !important;
}

.btn-success.tema-personalizado:hover {
  background-color: var(--cor-primaria-escura) !important;
  border-color: var(--cor-primaria-escura) !important;
}

/* Aplicar tema aos elementos específicos do sistema */
.tema-personalizado .table .livre {
  background-color: rgba(var(--cor-primaria-rgb), 0.1) !important;
}

.tema-personalizado .bg-success {
  background-color: var(--cor-primaria) !important;
}

.tema-personalizado .text-success {
  color: var(--cor-primaria) !important;
}

.tema-personalizado .border-success {
  border-color: var(--cor-primaria) !important;
}

/* Badges e alertas com tema personalizado */
.tema-personalizado .badge.bg-success {
  background-color: var(--cor-primaria) !important;
}

.tema-personalizado .alert-success {
  background-color: var(--cor-primaria-clara) !important;
  border-color: var(--cor-primaria) !important;
  color: var(--cor-primaria-escura) !important;
}

/* Links com tema personalizado */
.tema-personalizado a.text-success {
  color: var(--cor-primaria) !important;
}

.tema-personalizado a.text-success:hover {
  color: var(--cor-primaria-escura) !important;
}

/* ========================================
   BASE MOBILE FIRST (até 767px)
   ======================================== */

/* Destaque de datas - Mobile */
.data-atual-destaque {
  background-color: var(--destaque-data-bg) !important;
  color: var(--destaque-data-color) !important;
  padding: 3px 6px !important;
  border-radius: 4px !important;
  font-weight: 500 !important;
  border: 1px solid var(--destaque-data-border) !important;
  display: inline-block !important;
  font-size: var(--font-size-mobile) !important;
}

/* Modal detalhes - Mobile */
.modal-dialog {
  margin: var(--spacing-sm) !important;
  max-width: calc(100vw - 16px) !important;
}

.modal-header {
  padding: var(--spacing-md) !important;
  flex-wrap: wrap !important;
}

.modal-title {
  font-size: 18px !important;
  margin-bottom: var(--spacing-xs) !important;
  width: 100% !important;
}

/* Botões de navegação - Mobile */
.navigation-buttons {
  width: 100% !important;
  justify-content: center !important;
  margin: var(--spacing-xs) 0 0 0 !important;
  gap: var(--spacing-sm) !important;
}

.navigation-buttons .btn {
  width: 40px !important;
  height: 40px !important;
  font-size: 16px !important;
}

/* Reservas - Mobile */
.reserva-item {
  margin-bottom: var(--spacing-md) !important;
  padding: var(--spacing-md) !important;
}

.reserva-item h6 {
  font-size: var(--font-size-mobile) !important;
  margin-bottom: var(--spacing-sm) !important;
}

.reserva-item p {
  font-size: 13px !important;
  line-height: 1.4 !important;
}

/* Botões das reservas - Mobile */
.btn-group .btn {
  font-size: 12px !important;
  padding: 6px 8px !important;
}

/* ========================================
   TABLET (768px - 1023px)
   ======================================== */
@media (min-width: 768px) {

  /* Destaque de datas - Tablet */
  .data-atual-destaque {
    font-size: var(--font-size-tablet) !important;
    padding: 4px 8px !important;
  }

  /* Modal detalhes - Tablet */
  .modal-dialog {
    margin: var(--spacing-lg) auto !important;
    max-width: 90vw !important;
  }

  .modal-title {
    font-size: 20px !important;
    width: auto !important;
    margin-bottom: 0 !important;
  }

  /* Botões de navegação - Tablet */
  .navigation-buttons {
    width: auto !important;
    margin: 0 var(--spacing-md) 0 auto !important;
  }

  .navigation-buttons .btn {
    width: 38px !important;
    height: 38px !important;
  }

  /* Reservas - Tablet */
  .reserva-item h6 {
    font-size: var(--font-size-tablet) !important;
  }

  .reserva-item p {
    font-size: 14px !important;
  }

  /* Botões das reservas - Tablet */
  .btn-group .btn {
    font-size: 13px !important;
    padding: 7px 10px !important;
  }
}

/* ========================================
   DESKTOP (1024px+)
   ======================================== */
@media (min-width: 1024px) {

  /* Destaque de datas - Desktop */
  .data-atual-destaque {
    font-size: var(--font-size-desktop) !important;
    padding: 4px 8px !important;
  }

  /* Modal detalhes - Desktop */
  .modal-dialog {
    max-width: 800px !important;
    margin: var(--spacing-xl) auto !important;
  }

  .modal-title {
    font-size: 22px !important;
  }

  /* Botões de navegação - Desktop */
  .navigation-buttons .btn {
    width: 35px !important;
    height: 35px !important;
  }

  /* Reservas - Desktop */
  .reserva-item h6 {
    font-size: var(--font-size-desktop) !important;
  }

  .reserva-item p {
    font-size: 15px !important;
  }

  /* Botões das reservas - Desktop */
  .btn-group .btn {
    font-size: 14px !important;
    padding: 8px 12px !important;
  }
}

/* ========================================
   UTILITÁRIOS PARA AJUSTES RÁPIDOS
   ======================================== */

/* Classes para forçar visibilidade em diferentes telas */
.mobile-only {
  display: block !important;
}

.tablet-only,
.desktop-only {
  display: none !important;
}

@media (min-width: 768px) {
  .mobile-only {
    display: none !important;
  }

  .tablet-only {
    display: block !important;
  }

  .desktop-only {
    display: none !important;
  }
}

@media (min-width: 1024px) {

  .mobile-only,
  .tablet-only {
    display: none !important;
  }

  .desktop-only {
    display: block !important;
  }
}

/* Classes para espaçamentos responsivos */
.spacing-responsive {
  padding: var(--spacing-sm) !important;
}

@media (min-width: 768px) {
  .spacing-responsive {
    padding: var(--spacing-md) !important;
  }
}

@media (min-width: 1024px) {
  .spacing-responsive {
    padding: var(--spacing-lg) !important;
  }
}

/* ========================================
   DEBUG - REMOVER EM PRODUÇÃO
   ======================================== */
/*
.debug-breakpoint::before {
  content: "Mobile";
  position: fixed;
  top: 0;
  right: 0;
  background: red;
  color: white;
  padding: 4px 8px;
  z-index: 9999;
}

@media (min-width: 768px) {
  .debug-breakpoint::before { content: "Tablet"; background: orange; }
}

@media (min-width: 1024px) {
  .debug-breakpoint::before { content: "Desktop"; background: green; }
}
*/