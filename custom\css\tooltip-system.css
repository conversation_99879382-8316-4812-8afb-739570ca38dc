/**
 * Sistema unificado de tooltips para substituir mensagens inline
 * Estilos customizados para diferentes tipos de tooltips
 */

/* ===== TOOLTIPS DE ERRO ===== */
.tooltip-error .tooltip-inner {
    background-color: #dc3545 !important;
    color: #fff !important;
    font-weight: 500;
    max-width: 280px;
    font-size: 0.875rem;
    line-height: 1.4;
    text-align: left;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.tooltip-error .tooltip-arrow::before {
    border-top-color: #dc3545 !important;
    border-bottom-color: #dc3545 !important;
    border-left-color: #dc3545 !important;
    border-right-color: #dc3545 !important;
}

/* ===== TOOLTIPS DE AVISO ===== */
.tooltip-warning .tooltip-inner {
    background-color: #ffc107 !important;
    color: #000 !important;
    font-weight: 500;
    max-width: 220px;
    font-size: 0.875rem;
    line-height: 1.4;
    text-align: center;
    padding: 6px 10px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.tooltip-warning .tooltip-arrow::before {
    border-top-color: #ffc107 !important;
    border-bottom-color: #ffc107 !important;
    border-left-color: #ffc107 !important;
    border-right-color: #ffc107 !important;
}

/* ===== TOOLTIPS DE CONFLITO ===== */
.tooltip-conflict .tooltip-inner {
    background-color: #dc3545 !important;
    color: #fff !important;
    font-weight: 500;
    max-width: 300px;
    font-size: 0.875rem;
    line-height: 1.4;
    text-align: left;
    padding: 10px 14px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.4);
    border: 1px solid #b02a37;
}

.tooltip-conflict .tooltip-arrow::before {
    border-top-color: #dc3545 !important;
    border-bottom-color: #dc3545 !important;
    border-left-color: #dc3545 !important;
    border-right-color: #dc3545 !important;
}

/* ===== TOOLTIPS INFORMATIVOS ===== */
.tooltip-info .tooltip-inner {
    background-color: #0dcaf0 !important;
    color: #000 !important;
    font-weight: 500;
    max-width: 240px;
    font-size: 0.875rem;
    line-height: 1.4;
    text-align: center;
    padding: 8px 12px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(13, 202, 240, 0.3);
}

.tooltip-info .tooltip-arrow::before {
    border-top-color: #0dcaf0 !important;
    border-bottom-color: #0dcaf0 !important;
    border-left-color: #0dcaf0 !important;
    border-right-color: #0dcaf0 !important;
}

/* ===== ESTILOS PARA CAMPOS COM TOOLTIPS ATIVOS ===== */

/* Campos com erro */
.field-with-error-tooltip {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
    background-color: rgba(220, 53, 69, 0.05) !important;
    transition: all 0.15s ease-in-out;
}

.field-with-error-tooltip:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.35) !important;
}

/* Campos com aviso */
.field-with-warning-tooltip {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.2rem rgba(255, 193, 7, 0.25) !important;
    background-color: rgba(255, 193, 7, 0.08) !important;
    transition: all 0.15s ease-in-out;
}

.field-with-warning-tooltip:focus {
    border-color: #ffc107 !important;
    box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.35) !important;
}

/* Campos com conflito */
.field-with-conflict-tooltip {
    border-color: #dc3545 !important;
    background-color: rgba(220, 53, 69, 0.08) !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.3) !important;
    transition: all 0.15s ease-in-out;
}

.field-with-conflict-tooltip:focus {
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.4) !important;
}

/* ===== ANIMAÇÕES ===== */

/* Animação de entrada para tooltips */
.tooltip.show {
    animation: tooltipFadeIn 0.3s ease-out;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: var(--bs-tooltip-opacity);
        transform: scale(1);
    }
}

/* Animação sutil para campos com problemas */
.field-with-error-tooltip,
.field-with-warning-tooltip,
.field-with-conflict-tooltip {
    animation: fieldHighlight 0.5s ease-in-out;
}

@keyframes fieldHighlight {
    0%, 100% {
        transform: translateX(0);
    }
    25% {
        transform: translateX(-2px);
    }
    75% {
        transform: translateX(2px);
    }
}

/* ===== RESPONSIVIDADE ===== */

/* Ajustes para telas pequenas */
@media (max-width: 768px) {
    .tooltip-error .tooltip-inner,
    .tooltip-conflict .tooltip-inner {
        max-width: 250px;
        font-size: 0.8rem;
        padding: 6px 10px;
    }
    
    .tooltip-warning .tooltip-inner,
    .tooltip-info .tooltip-inner {
        max-width: 200px;
        font-size: 0.8rem;
        padding: 5px 8px;
    }
}

@media (max-width: 480px) {
    .tooltip-error .tooltip-inner,
    .tooltip-conflict .tooltip-inner {
        max-width: 200px;
        font-size: 0.75rem;
    }
    
    .tooltip-warning .tooltip-inner,
    .tooltip-info .tooltip-inner {
        max-width: 180px;
        font-size: 0.75rem;
    }
}

/* ===== COMPATIBILIDADE COM TEMA ESCURO ===== */
@media (prefers-color-scheme: dark) {
    .tooltip-warning .tooltip-inner {
        background-color: #ffca2c !important;
        color: #000 !important;
    }
    
    .tooltip-info .tooltip-inner {
        background-color: #31d2f2 !important;
        color: #000 !important;
    }
}

/* ===== OVERRIDE PARA REMOVER ESTILOS ANTIGOS ===== */

/* Remover estilos do sistema antigo de "Data Antiga" */
.data-anterior-hoje::before {
    display: none !important;
}

.data-anterior-hoje {
    border: none !important;
    background-color: transparent !important;
    box-shadow: none !important;
    animation: none !important;
}

/* Garantir que tooltips tenham prioridade sobre popovers antigos */
.tooltip {
    z-index: 1090 !important;
}

/* ===== ACESSIBILIDADE ===== */

/* Melhorar contraste para usuários com deficiência visual */
@media (prefers-contrast: high) {
    .tooltip-error .tooltip-inner {
        background-color: #a71e2a !important;
        border: 2px solid #fff;
    }
    
    .tooltip-warning .tooltip-inner {
        background-color: #cc9a00 !important;
        border: 2px solid #000;
        color: #fff !important;
    }
    
    .tooltip-conflict .tooltip-inner {
        background-color: #a71e2a !important;
        border: 2px solid #fff;
    }
    
    .tooltip-info .tooltip-inner {
        background-color: #0a9fb8 !important;
        border: 2px solid #fff;
        color: #fff !important;
    }
}

/* Reduzir animações para usuários sensíveis a movimento */
@media (prefers-reduced-motion: reduce) {
    .tooltip.show {
        animation: none;
    }
    
    .field-with-error-tooltip,
    .field-with-warning-tooltip,
    .field-with-conflict-tooltip {
        animation: none;
    }
    
    .field-with-error-tooltip,
    .field-with-warning-tooltip,
    .field-with-conflict-tooltip {
        transition: none;
    }
}
