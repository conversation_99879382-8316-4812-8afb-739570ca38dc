<?php
session_start();

// Verificar autenticação
if (!isset($_SESSION['user_id']) || !isset($_SESSION['user_pousada_id'])) {
    http_response_code(401);
    echo json_encode(['erro' => 'Não autenticado']);
    exit;
}

// Incluir configuração do banco
include_once("../../config.php");

// Verificar se os parâmetros foram enviados
if (!isset($_POST['uh']) || !isset($_POST['data'])) {
    http_response_code(400);
    echo json_encode(['erro' => 'Parâmetros obrigatórios não fornecidos']);
    exit;
}

$pousada_id = $_SESSION['user_pousada_id'];
$uh = $_POST['uh'];
$data = $_POST['data'];

// Converter data do formato brasileiro para formato MySQL
$data_mysql = DateTime::createFromFormat('d/m/Y', $data);
if (!$data_mysql) {
    http_response_code(400);
    echo json_encode(['erro' => 'Formato de data inválido']);
    exit;
}
$data_formatada = $data_mysql->format('Y-m-d');

try {
    // Buscar todas as reservas que afetam esta UH nesta data
    $sql = "SELECT r.id, r.hospede_id, r.uh, r.numacomp, r.dataentrada, r.datasaida,
                   r.horaentrada, r.horasaida, r.acompanhantes, h.nome AS hospede_nome
            FROM reservas r
            LEFT JOIN hospedes h ON r.hospede_id = h.id
            WHERE r.pousada_id = ? AND r.uh = ? AND
            (
                (DATE(r.dataentrada) = ?) OR
                (DATE(r.datasaida) = ?) OR
                (DATE(r.dataentrada) < ? AND DATE(r.datasaida) > ?)
            )
            ORDER BY r.dataentrada ASC, r.horaentrada ASC";

    $stmt = $conn->prepare($sql);
    if (!$stmt) {
        throw new Exception("Erro na preparação da consulta: " . $conn->error);
    }

    $stmt->bind_param('isssss', $pousada_id, $uh, $data_formatada, $data_formatada, $data_formatada, $data_formatada);
    
    if (!$stmt->execute()) {
        throw new Exception("Erro na execução da consulta: " . $stmt->error);
    }

    $result = $stmt->get_result();
    $reservas = [];

    while ($row = $result->fetch_assoc()) {
        // Formatar as datas e horas para exibição
        $row['dataentrada_formatada'] = date('d/m/Y', strtotime($row['dataentrada']));
        $row['datasaida_formatada'] = date('d/m/Y', strtotime($row['datasaida']));
        $row['horaentrada_formatada'] = $row['horaentrada'] ? date('H:i', strtotime($row['horaentrada'])) : '';
        $row['horasaida_formatada'] = $row['horasaida'] ? date('H:i', strtotime($row['horasaida'])) : '';
        
        $reservas[] = $row;
    }

    $stmt->close();

    // Retornar os dados em JSON
    echo json_encode([
        'sucesso' => true,
        'reservas' => $reservas,
        'total' => count($reservas),
        'uh' => $uh,
        'data' => $data,
        'data_formatada' => $data_formatada
    ]);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['erro' => 'Erro interno do servidor: ' . $e->getMessage()]);
}
?>
