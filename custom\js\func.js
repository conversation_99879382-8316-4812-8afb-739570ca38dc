// Calcular idade
const nascElement = document.getElementsByName('nasc')[0];
if (nascElement) {
    nascElement.addEventListener('change', function () {
        const dataNascimento = new Date(this.value);
        const hoje = new Date();
        let idade = hoje.getFullYear() - dataNascimento.getFullYear();
        const mes = hoje.getMonth() - dataNascimento.getMonth();
        if (mes < 0 || (mes === 0 && hoje.getDate() < dataNascimento.getDate())) {
            idade--;
        }
        const idadeInput = document.getElementsByName('idade')[0];
        if (idadeInput) {
            idadeInput.value = idade.toString();
        }
    });
}
// Formatar CPF e validar
const cpfElement = document.getElementsByName('cpf')[0];
if (cpfElement) {
    cpfElement.addEventListener('input', function () {
        formatarEValidarCPF(this);
    });
    // Verificação completa ao sair do campo
    cpfElement.addEventListener('blur', function () {
        const cpf = this.value.replace(/\D/g, '');
        // Verificar tamanho
        if (cpf.length > 0 && cpf.length < 11) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            this.title = 'CPF incompleto. Digite todos os 11 dígitos.';
            // Mostrar mensagem de CPF inválido para CPF incompleto
            const feedbackDiv = document.getElementById('cpf-feedback');
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
            }
            return;
        }
        else if (cpf.length > 11) {
            this.classList.remove('is-valid');
            this.classList.add('is-invalid');
            this.title = 'CPF com muitos dígitos. Deve ter apenas 11 dígitos.';
            // Mostrar mensagem de CPF inválido para CPF com muitos dígitos
            const feedbackDiv = document.getElementById('cpf-feedback');
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
            }
            return;
        }
        // Verificar se CPF já existe (apenas se tiver 11 dígitos E for válido)
        if (cpf.length === 11 && validarCPF(this.value)) {
            verificarCPFDuplicado(cpf, this);
        }
    });
}
// Nova função para verificar CPF duplicado com controle do botão
function verificarCPFDuplicado(cpf, inputElement) {
    var _a;
    // Detectar contexto baseado no título da página
    const tituloElement = document.querySelector('h3');
    const isEdicao = tituloElement ? ((_a = tituloElement.textContent) === null || _a === void 0 ? void 0 : _a.includes('Editar Hospede')) || false : false;
    const submitButton = document.querySelector('button[type="submit"]');
    const feedbackDiv = document.getElementById('cpf-feedback');
    console.log('Contexto detectado:', isEdicao ? 'Edição' : 'Novo cadastro');
    console.log('Título encontrado:', tituloElement ? tituloElement.textContent : 'Nenhum');
    // Só verificar duplicidade se CPF for válido e tiver 11 dígitos
    if (cpf.length < 11) {
        if (feedbackDiv) {
            feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
        }
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'CPF Incompleto';
            submitButton.classList.add('btn-secondary');
            submitButton.classList.remove('btn-primary');
        }
        return;
    }
    if (!validarCPF(cpf)) {
        if (feedbackDiv) {
            feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
        }
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'CPF Inválido';
            submitButton.classList.add('btn-secondary');
            submitButton.classList.remove('btn-primary');
        }
        return;
    }
    // Construir URL da requisição
    let url = 'verificar_cpf.php?cpf=' + encodeURIComponent(cpf);
    console.log('URL da requisição:', url);
    // Se estiver na tela de edição, adicionar o hospede_id para excluir da verificação
    const formElement = inputElement.closest('form');
    console.log('Form element:', formElement);
    console.log('Form ID:', formElement ? formElement.id : 'não encontrado');
    if (formElement && formElement.id === 'editarHospede') {
        const hospedeIdInput = formElement.querySelector('input[name="id"]');
        console.log('Hospede ID input:', hospedeIdInput);
        console.log('Hospede ID value:', hospedeIdInput ? hospedeIdInput.value : 'não encontrado');
        if (hospedeIdInput && hospedeIdInput.value) {
            url += '&hospede_id=' + encodeURIComponent(hospedeIdInput.value);
        }
    }
    console.log('URL final:', url);
    fetch(url)
        .then(response => response.json())
        .then((data) => {
        console.log('Resposta do servidor:', data);
        if (data.existe) {
            // CPF duplicado - desabilitar botão e mostrar erro
            inputElement.classList.remove('is-valid');
            inputElement.classList.add('is-invalid');
            inputElement.title = 'CPF já cadastrado para: ' + (data.nome_hospede || '');
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF já cadastrado para: ' + (data.nome_hospede || '') + '</b>';
                feedbackDiv.style.color = 'red';
            }
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'CPF Duplicado - Não é possível registrar';
                submitButton.classList.add('btn-danger');
                submitButton.classList.remove('btn-primary', 'btn-secondary');
            }
        }
        else {
            // CPF disponível - habilitar botão
            inputElement.classList.remove('is-invalid');
            inputElement.classList.add('is-valid');
            inputElement.title = '';
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF disponível</b>';
                feedbackDiv.style.color = 'green';
            }
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Registrar';
                submitButton.classList.add('btn-primary');
                submitButton.classList.remove('btn-danger', 'btn-secondary');
            }
        }
    })
        .catch(error => {
        console.error('Erro ao verificar CPF:', error);
        // Em caso de erro, permitir o envio (fallback)
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = 'Registrar';
            submitButton.classList.add('btn-primary');
            submitButton.classList.remove('btn-danger', 'btn-secondary');
        }
    });
}
// Função para formatar e validar CPF
function formatarEValidarCPF(input) {
    let cpf = input.value.replace(/\D/g, ''); // Remove caracteres não numéricos
    if (cpf.length <= 11) {
        cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
        cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
        cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
    }
    input.value = cpf;
    const feedbackDiv = document.getElementById('cpf-feedback');
    const submitButton = document.querySelector('button[type="submit"]');
    if (cpf.length === 14) { // Verifica se o CPF está completo
        if (validarCPF(cpf)) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
            input.title = '';
            // CPF válido, mas ainda precisa verificar duplicidade
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>Verificando disponibilidade...</b>';
                feedbackDiv.style.color = 'orange';
            }
        }
        else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
            input.title = 'CPF inválido. Verifique a digitação.';
            // CPF inválido - desabilitar botão
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                feedbackDiv.style.color = 'red';
            }
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'CPF Inválido';
                submitButton.classList.add('btn-secondary');
                submitButton.classList.remove('btn-primary', 'btn-danger');
            }
        }
    }
    else {
        input.classList.remove('is-valid', 'is-invalid');
        // Campo vazio ou incompleto
        if (cpf.length === 0) {
            // Campo vazio - habilitar botão e limpar feedback
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '';
            }
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Registrar';
                submitButton.classList.add('btn-primary');
                submitButton.classList.remove('btn-danger', 'btn-secondary');
            }
        }
        else {
            // Campo incompleto - desabilitar botão
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF incompleto</b>.';
                feedbackDiv.style.color = 'orange';
            }
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'CPF Incompleto';
                submitButton.classList.add('btn-secondary');
                submitButton.classList.remove('btn-primary', 'btn-danger');
            }
        }
    }
}
// Validar CPF
function validarCPF(cpf) {
    cpf = cpf.replace(/[^\d]+/g, '');
    if (cpf === '')
        return false;
    // Elimina CPFs invalidos conhecidos
    if (cpf.length !== 11 ||
        cpf === "00000000000" ||
        cpf === "11111111111" ||
        cpf === "22222222222" ||
        cpf === "33333333333" ||
        cpf === "44444444444" ||
        cpf === "55555555555" ||
        cpf === "66666666666" ||
        cpf === "77777777777" ||
        cpf === "88888888888" ||
        cpf === "99999999999")
        return false;
    // Valida 1o digito
    let add = 0;
    for (let i = 0; i < 9; i++)
        add += parseInt(cpf.charAt(i)) * (10 - i);
    let rev = 11 - (add % 11);
    if (rev === 10 || rev === 11)
        rev = 0;
    if (rev !== parseInt(cpf.charAt(9)))
        return false;
    // Valida 2o digito
    add = 0;
    for (let i = 0; i < 10; i++)
        add += parseInt(cpf.charAt(i)) * (11 - i);
    rev = 11 - (add % 11);
    if (rev === 10 || rev === 11)
        rev = 0;
    if (rev !== parseInt(cpf.charAt(10)))
        return false;
    return true;
}
// Formatar CPF ao carregar a página
document.addEventListener('DOMContentLoaded', function () {
    const cpfInput = document.getElementsByName('cpf')[0];
    if (cpfInput && cpfInput.value) {
        formatarEValidarCPF(cpfInput);
    }
});
const printButton = document.getElementById('print-button');
if (printButton) {
    printButton.addEventListener('click', function () {
        window.print();
    });
}
// Função para fazer Enter funcionar como Tab nos formulários
function setupEnterAsTab() {
    // Seleciona todos os inputs, selects e textareas dos formulários
    const formElements = document.querySelectorAll('form input, form select, form textarea');
    formElements.forEach(function (element) {
        element.addEventListener('keydown', function (event) {
            // Verifica se a tecla pressionada é Enter (código 13)
            if (event.key === 'Enter' || event.keyCode === 13) {
                // Previne o comportamento padrão do Enter
                event.preventDefault();
                // Encontra todos os elementos focáveis do formulário atual
                const form = element.closest('form');
                if (!form)
                    return;
                const focusableElements = form.querySelectorAll('input:not([type="hidden"]):not([disabled]):not([readonly]), ' +
                    'select:not([disabled]), ' +
                    'textarea:not([disabled]):not([readonly]), ' +
                    'button:not([disabled])');
                // Converte NodeList para Array para usar indexOf
                const focusableArray = Array.from(focusableElements);
                const currentIndex = focusableArray.indexOf(element);
                // Move para o próximo elemento focável
                if (currentIndex > -1 && currentIndex < focusableArray.length - 1) {
                    const nextElement = focusableArray[currentIndex + 1];
                    nextElement.focus();
                    // Se for um input de texto, seleciona todo o conteúdo
                    if (nextElement.type === 'text' ||
                        nextElement.type === 'email' ||
                        nextElement.type === 'tel' ||
                        nextElement.tagName === 'TEXTAREA') {
                        nextElement.select();
                    }
                }
                else if (currentIndex === focusableArray.length - 1) {
                    // Se estiver no último campo, pode submeter o formulário ou voltar ao primeiro
                    // Descomente a linha abaixo se quiser submeter automaticamente:
                    // form.submit();
                    // Ou voltar ao primeiro campo:
                    if (focusableArray.length > 0) {
                        focusableArray[0].focus();
                    }
                }
            }
        });
    });
}
// Executa a função quando o DOM estiver carregado
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', setupEnterAsTab);
}
else {
    setupEnterAsTab();
}
// Também executa quando novos formulários são carregados via AJAX
// (útil para formulários carregados dinamicamente)
function reinitializeEnterAsTab() {
    setupEnterAsTab();
}
// FUNÇÃO REMOVIDA: verificarDisponibilidadeUH
// A nova implementação está em verificacao-disponibilidade-local.ts
// Função para mostrar aviso de conflito de reserva usando popover
function mostrarAvisoConflito(elemento, mensagem) {
    // Remove avisos anteriores
    const avisosAnteriores = document.querySelectorAll('.aviso-conflito');
    avisosAnteriores.forEach(aviso => aviso.remove());
    // Criar popover para aviso de conflito
    elemento.setAttribute('data-bs-toggle', 'popover');
    elemento.setAttribute('data-bs-placement', 'bottom');
    elemento.setAttribute('data-bs-content', mensagem);
    elemento.setAttribute('data-bs-trigger', 'manual');
    elemento.setAttribute('data-bs-html', 'true');
    elemento.classList.add('aviso-conflito');
    // Configurar popover Bootstrap
    const popover = new window.bootstrap.Popover(elemento, {
        container: 'body',
        customClass: 'popover-conflito',
        placement: 'bottom'
    });
    // Mostrar popover
    popover.show();
    // Destacar o campo de forma mais sutil
    elemento.classList.add('border-danger');
    elemento.style.backgroundColor = 'rgba(220, 53, 69, 0.05)';
    elemento.style.boxShadow = '0 0 0 0.2rem rgba(220, 53, 69, 0.25)';
    // Auto-remover após 8 segundos
    setTimeout(() => {
        popover.hide();
        removerAvisoConflito(elemento);
    }, 8000);
    // Remover ao clicar fora
    const removerAoClicarFora = (event) => {
        if (!elemento.contains(event.target)) {
            popover.hide();
            removerAvisoConflito(elemento);
            document.removeEventListener('click', removerAoClicarFora);
        }
    };
    setTimeout(() => {
        document.addEventListener('click', removerAoClicarFora);
    }, 100);
}
// Função para remover aviso de conflito
function removerAvisoConflito(elemento) {
    // Destruir popover se existir
    const popoverInstance = window.bootstrap.Popover.getInstance(elemento);
    if (popoverInstance) {
        popoverInstance.dispose();
    }
    // Remover atributos do popover
    elemento.removeAttribute('data-bs-toggle');
    elemento.removeAttribute('data-bs-placement');
    elemento.removeAttribute('data-bs-content');
    elemento.removeAttribute('data-bs-trigger');
    elemento.removeAttribute('data-bs-html');
    elemento.classList.remove('aviso-conflito');
    // Remover estilos do elemento
    elemento.classList.remove('border-danger');
    elemento.style.backgroundColor = '';
    elemento.style.boxShadow = '';
}
// Função para verificar conflitos de reserva (reutilizável)
function verificarConflitosReserva(uhInput, dataEntradaInput, horaEntradaInput, dataSaidaInput, horaSaidaInput, container, callback) {
    const uh = uhInput.value;
    const dataEntrada = dataEntradaInput.value;
    const horaEntrada = horaEntradaInput.value;
    const dataSaida = dataSaidaInput ? dataSaidaInput.value : dataEntrada; // IMPLEMENTAR: usar data de saída
    const horaSaida = horaSaidaInput ? horaSaidaInput.value : '12:00'; // IMPLEMENTAR: usar hora de saída
    if (!uh || !dataEntrada) {
        return;
    }
    // IMPLEMENTAR: Incluir data e hora de saída na requisição
    fetch('verificar_disponibilidade.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `uh=${encodeURIComponent(uh)}&dataEntrada=${encodeURIComponent(dataEntrada)}&horaEntrada=${encodeURIComponent(horaEntrada)}&dataSaida=${encodeURIComponent(dataSaida)}&horaSaida=${encodeURIComponent(horaSaida)}`
    })
        .then(response => response.json())
        .then((data) => {
        if (data.erro) {
            console.error('Erro:', data.erro);
            return;
        }
        if (callback) {
            callback(data);
        }
        // Mostrar/ocultar aviso de conflito
        if (container) {
            mostrarAvisoConflito(container, data.mensagem || 'Conflito detectado');
        }
    })
        .catch(error => {
        console.error('3_Erro na verificação:', error);
    });
}
// Função para configurar verificação automática de conflitos
function configurarVerificacaoConflitos(uhInput, dataEntradaInput, horaEntradaInput, container) {
    function verificar() {
        verificarConflitosReserva(uhInput, dataEntradaInput, horaEntradaInput, undefined, undefined, container);
    }
    // Adicionar event listeners
    if (dataEntradaInput) {
        dataEntradaInput.addEventListener('change', verificar);
    }
    if (uhInput) {
        uhInput.addEventListener('change', verificar);
        uhInput.addEventListener('blur', verificar);
    }
    if (horaEntradaInput) {
        horaEntradaInput.addEventListener('change', verificar);
    }
    // Verificar imediatamente
    verificar();
}
// REMOVIDO: Exportação da função antiga
// REMOVIDO: Declaração duplicada das interfaces Window
// Exportar funções para o window
// Remover estas linhas:
// window.mostrarAvisoConflito = mostrarAvisoConflito;
// window.removerAvisoConflito = removerAvisoConflito;
// window.verificarConflitosReserva = verificarConflitosReserva;
// window.configurarVerificacaoConflitos = configurarVerificacaoConflitos;
// window.reinitializeEnterAsTab = reinitializeEnterAsTab;
// window.validarCPF = validarCPF;
// window.formatarEValidarCPF = formatarEValidarCPF;
// window.verificarCPFDuplicado = verificarCPFDuplicado;
// Substituir export {}; por:
export { validarCPF, formatarEValidarCPF, verificarCPFDuplicado, mostrarAvisoConflito, removerAvisoConflito, verificarConflitosReserva, configurarVerificacaoConflitos, reinitializeEnterAsTab };
