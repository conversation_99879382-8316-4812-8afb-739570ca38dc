// Imports ES6 para os módulos
import { ModalDetalhes } from './modal-detalhes.js';
import { ModalHospede } from './modal-hospede.js';
import { ModalReserva } from './modal-reserva.js';
import { ModalEditarReserva } from './modal-editar-reserva.js';
// Arquivo principal para coordenar os módulos do mapa de UH
// Inicialização dos módulos quando o DOM estiver carregado
class MapaUHMain {
    constructor() {
        this.modalDetalhes = null;
        this.modalHospede = null;
        this.modalReserva = null;
        this.modalEditarReserva = null;
        this.init();
    }
    init() {
        // Aguardar o DOM estar completamente carregado
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.initializeModules());
        }
        else {
            this.initializeModules();
        }
    }
    initializeModules() {
        try {
            // Inicializar os módulos diretamente (sem verificações typeof)
            this.modalDetalhes = new ModalDetalhes();
            this.modalHospede = new ModalHospede();
            this.modalReserva = new ModalReserva();
            this.modalEditarReserva = new ModalEditarReserva();
            window.modalEditarReserva = this.modalEditarReserva;
            this.setupModuleCommunication();
            console.log('Módulos do Mapa UH inicializados com sucesso');
        }
        catch (error) {
            console.error('Erro ao inicializar módulos do Mapa UH:', error);
        }
    }
    setupModuleCommunication() {
        // Configurar eventos customizados para comunicação entre módulos
        // Event listener para o botão Nova Reserva
        document.addEventListener('click', (event) => {
            const target = event.target;
            if (target && target.id === 'novaReservaLink') {
                event.preventDefault();
                const uh = target.getAttribute('data-uh');
                const data = target.getAttribute('data-data');
                if (uh && data && this.modalHospede) {
                    // Fechar o modal de detalhes
                    const reservaModalElement = document.getElementById('reservaModal');
                    if (reservaModalElement) {
                        const reservaModal = bootstrap.Modal.getInstance(reservaModalElement);
                        if (reservaModal) {
                            reservaModal.hide();
                        }
                    }
                    // Abrir o modal de seleção de hóspede
                    this.modalHospede.abrirModalSelecaoHospede(uh, data);
                }
            }
        });
        // Quando um hóspede for selecionado no modal de hóspede,
        // abrir o modal de reserva
        document.addEventListener('hospedeSelected', ((event) => {
            const { hospede, uh, data } = event.detail;
            if (this.modalReserva) {
                this.modalReserva.carregarFormularioReserva(uh, data, hospede);
            }
        }));
        // Quando uma reserva for salva com sucesso, recarregar a página
        document.addEventListener('reservaSalva', () => {
            location.reload();
        });
        // Quando um hóspede for cadastrado com sucesso,
        // abrir o modal de reserva
        document.addEventListener('hospedeCadastrado', ((event) => {
            const { hospede, uh, data } = event.detail;
            if (this.modalReserva) {
                this.modalReserva.carregarFormularioReserva(uh, data, hospede);
            }
        }));
    }
    // Métodos públicos para acesso aos módulos
    getModalDetalhes() {
        return this.modalDetalhes;
    }
    getModalHospede() {
        return this.modalHospede;
    }
    getModalReserva() {
        return this.modalReserva;
    }
}
// Funções globais para manter compatibilidade com código existente
let mapaUHMain;
// Inicializar quando o script for carregado
mapaUHMain = new MapaUHMain();
// Funções globais para compatibilidade
// Corrigir as funções (linhas 166-207)
function salvarHospedeCompleto(form) {
    if (mapaUHMain && mapaUHMain.modalHospede) {
        return mapaUHMain.modalHospede.salvarHospedeCompleto(form);
    }
    console.error('ModalHospede não inicializado');
}
function carregarFormularioCompleto(uh, data) {
    if (mapaUHMain && mapaUHMain.modalHospede) {
        return mapaUHMain.modalHospede.carregarFormularioCompleto(uh, data);
    }
    console.error('ModalHospede não inicializado');
}
function carregarFormularioReserva(uh, data, hospede = null) {
    if (mapaUHMain && mapaUHMain.modalReserva) {
        return mapaUHMain.modalReserva.carregarFormularioReserva(uh, data, hospede);
    }
    console.error('ModalReserva não inicializado');
}
function verificarDisponibilidadeModal() {
    if (mapaUHMain && mapaUHMain.modalReserva) {
        return mapaUHMain.modalReserva.verificarDisponibilidadeModal();
    }
    console.error('ModalReserva não inicializado');
}
function salvarReservaCompleta(form) {
    if (mapaUHMain && mapaUHMain.modalReserva) {
        return mapaUHMain.modalReserva.salvarReservaCompleta(form);
    }
    console.error('ModalReserva não inicializado');
}
function converterDataParaISO(dataStr) {
    if (mapaUHMain && mapaUHMain.modalReserva) {
        return mapaUHMain.modalReserva.converterDataParaISO(dataStr);
    }
    console.error('ModalReserva não inicializado');
    return null;
}
// Exportar para uso global
window.mapaUHMain = mapaUHMain;
window.salvarHospedeCompleto = salvarHospedeCompleto;
window.carregarFormularioCompleto = carregarFormularioCompleto;
window.carregarFormularioReserva = carregarFormularioReserva;
window.verificarDisponibilidadeModal = verificarDisponibilidadeModal;
window.salvarReservaCompleta = salvarReservaCompleta;
window.converterDataParaISO = converterDataParaISO;
export { MapaUHMain, salvarHospedeCompleto, carregarFormularioCompleto, carregarFormularioReserva, verificarDisponibilidadeModal, salvarReservaCompleta, converterDataParaISO };
