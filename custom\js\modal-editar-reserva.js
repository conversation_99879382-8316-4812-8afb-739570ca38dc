class ModalEditarReserva {
    constructor() {
        this.modal = null;
        this.container = null;
        this.init();
    }
    init() {
        this.modal = document.getElementById('editarReservaModal');
        this.container = document.getElementById('formularioEditarContainer');
        if (!this.modal || !this.container) {
            console.error('Modal de editar reserva não encontrado');
            return;
        }
        // Carregar script func.js no início para garantir que as funções estejam disponíveis
        if (!document.querySelector('script[src="custom/js/func.js"]')) {
            const script = document.createElement('script');
            script.src = 'custom/js/func.js';
            document.head.appendChild(script);
        }
    }
    carregarFormularioEdicao(reservaId, hospedeNome) {
        if (!this.modal || !this.container) {
            console.error('Modal não inicializado');
            return;
        }
        // Atualizar cabeçalho do modal
        const modalTitle = this.modal.querySelector('.modal-title');
        if (modalTitle) {
            modalTitle.textContent = 'Editar Reserva';
        }
        // Mostrar modal com loading
        const modalInstance = new bootstrap.Modal(this.modal);
        modalInstance.show();
        // Mostrar loading
        this.container.innerHTML = `
            <div class="text-center">
                <div class="spinner-border" role="status">
                    <span class="visually-hidden">Carregando...</span>
                </div>
                <p>Carregando formulário de edição...</p>
            </div>
        `;
        // Carregar formulário via AJAX
        fetch('carregar_formulario_edicao.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `reserva_id=${encodeURIComponent(reservaId)}&hospede_nome=${encodeURIComponent(hospedeNome)}`
        })
            .then(response => response.json())
            .then((data) => {
            if (data.success) {
                if (this.container && data.html) {
                    this.container.innerHTML = data.html;
                    // Configurar formulário após carregar
                    setTimeout(() => {
                        this.setupFormulario();
                    }, 100);
                }
            }
            else {
                if (this.container) {
                    this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário de edição.</div>';
                }
            }
        })
            .catch(error => {
            console.error('Erro:', error);
            if (this.container) {
                this.container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário de edição.</div>';
            }
        });
    }
    setupFormulario() {
        const form = document.getElementById('formEditarReserva');
        if (!form) {
            console.error('Formulário de edição não encontrado');
            return;
        }
        // Configurar validação e eventos
        this.setupFormValidation();
        this.setupDisponibilidadeCheck();
        // Não precisamos carregar o script novamente aqui, já foi carregado no init()
        // Removido: const script = document.createElement('script');
        // Removido: script.src = 'custom/js/func.js';
        // Removido: document.head.appendChild(script);
        // Adicionar evento de submit
        form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.salvarEdicaoReserva(form);
        });
    }
    setupFormValidation() {
        const form = document.getElementById('formEditarReserva');
        if (form && this.container) {
            // Não fazemos mais verificação de conflitos aqui
            // O sistema já possui outro mecanismo que funciona melhor no momento do registro
        }
    }
    setupDisponibilidadeCheck() {
        const uhField = document.querySelector('input[name="uh"]');
        const dataEntradaField = document.querySelector('input[name="dataentrada"]');
        const horaEntradaField = document.querySelector('input[name="horaentrada"]');
        const dataSaidaField = document.querySelector('input[name="datasaida"]');
        const horaSaidaField = document.querySelector('input[name="horasaida"]');
        const reservaIdField = document.querySelector('input[name="reserva_id"]');
        if (uhField && dataEntradaField) {
            // MODIFICAÇÃO: Incluir campos de data/hora de saída na verificação
            [uhField, dataEntradaField, horaEntradaField, dataSaidaField, horaSaidaField].forEach(field => {
                if (field) {
                    field.addEventListener('change', () => {
                        this.verificarDisponibilidadeEdicao();
                        // NOVA FUNCIONALIDADE: Verificar data anterior
                        if (field.name === 'dataentrada' || field.name === 'datasaida') {
                            this.verificarDataAnterior(field);
                        }
                    });
                    // NOVA FUNCIONALIDADE: Verificar conflito ao sair do campo (blur)
                    field.addEventListener('blur', () => {
                        this.verificarDisponibilidadeEdicao();
                        if (field.name === 'dataentrada' || field.name === 'datasaida') {
                            this.verificarDataAnterior(field);
                        }
                    });
                }
            });
            // Não fazemos mais verificação de conflito de datas aqui
            // O sistema já possui outro mecanismo que funciona melhor no momento do registro
        }
    }
    // NOVA FUNÇÃO: Verificar se a data é anterior a hoje
    verificarDataAnterior(campoData) {
        if (!campoData.value)
            return;
        const agora = new Date();
        // Obter a hora correspondente ao campo de data
        let campoHora = null;
        if (campoData.name === 'dataentrada') {
            campoHora = document.querySelector('input[name="horaentrada"]');
        }
        else if (campoData.name === 'datasaida') {
            campoHora = document.querySelector('input[name="horasaida"]');
        }
        // Criar data/hora selecionada
        const dataSelecionada = new Date(campoData.value);
        // Se há campo de hora preenchido, usar a hora; senão usar 23:59 (final do dia)
        if (campoHora && campoHora.value) {
            const [horas, minutos] = campoHora.value.split(':').map(Number);
            dataSelecionada.setHours(horas, minutos, 0, 0);
        }
        else {
            // Se não há hora definida, considerar o final do dia (23:59)
            dataSelecionada.setHours(23, 59, 59, 999);
        }
        // Remover classe anterior primeiro
        campoData.classList.remove('data-anterior-hoje');
        // Adicionar classe se a data/hora for anterior ao momento atual
        if (dataSelecionada < agora) {
            campoData.classList.add('data-anterior-hoje');
            console.log(`Aviso: Data/hora ${campoData.value} ${(campoHora === null || campoHora === void 0 ? void 0 : campoHora.value) || '23:59'} é anterior ao momento atual`);
        }
    }
    verificarDisponibilidadeEdicao() {
        // Não fazemos mais verificação de conflito de datas aqui
        // O sistema já possui outro mecanismo que funciona melhor no momento do registro
    }
    verificarDisponibilidadeEdicaoOriginal() {
        const uhField = document.querySelector('input[name="uh"]');
        const dataField = document.querySelector('input[name="dataentrada"]');
        const horaField = document.querySelector('input[name="horaentrada"]');
        const reservaIdField = document.querySelector('input[name="reserva_id"]');
        const dataSaidaField = document.querySelector('input[name="datasaida"]');
        const horaSaidaField = document.querySelector('input[name="horasaida"]');
        if (uhField && dataField && uhField.value && dataField.value && reservaIdField) {
            // Remover avisos anteriores usando a função do func.js
            if (typeof removerAvisoConflito === 'function') {
                removerAvisoConflito(dataField);
                removerAvisoConflito(uhField);
                // NOVO: Remover avisos dos campos de saída também
                if (dataSaidaField)
                    removerAvisoConflito(dataSaidaField);
            }
            else {
                // Fallback caso a função não esteja disponível
                const avisosAnteriores = document.querySelectorAll('.aviso-conflito');
                avisosAnteriores.forEach(aviso => aviso.remove());
                // Remover destaque anterior de todos os campos
                [dataField, uhField, dataSaidaField].forEach(field => {
                    if (field) {
                        field.classList.remove('border-danger');
                        field.style.border = '';
                        field.style.backgroundColor = '';
                    }
                });
            }
            console.log('Verificando disponibilidade para UH:', uhField.value, 'Data:', dataField.value, 'ID Reserva:', reservaIdField.value);
            // Usar apenas a função melhorada
            if (typeof verificarDisponibilidadeUHMelhorada === 'function') {
                verificarDisponibilidadeUHMelhorada(uhField.value, dataField.value, horaField ? horaField.value : '13:00', (data, error) => {
                    this.processarResultadoVerificacaoEdicao(data, error, dataField, uhField);
                }, reservaIdField.value ? parseInt(reservaIdField.value, 10) : null, // Converter string para number
                dataSaidaField ? dataSaidaField.value : undefined, horaSaidaField ? horaSaidaField.value : undefined);
            }
            else {
                console.error('Função verificarDisponibilidadeUHMelhorada não encontrada');
            }
        }
    }
    processarResultadoVerificacaoEdicao(data, error, dataField, uhField) {
        var _a;
        if (error) {
            console.error('Erro na verificação:', error);
            return;
        }
        if (data) {
            // Adicionar diagnóstico
            console.log('Dados recebidos em modal-editar-reserva:', data);
            // Verificar se há conflito
            if (!data.disponivel) {
                // Usar função do func.js se disponível
                if (typeof mostrarAvisoConflito === 'function') {
                    let mensagem = data.mensagem || 'UH não disponível no período selecionado.';
                    if (data.fonte) {
                        const fonteTexto = data.fonte === 'local' ? '' :
                            data.fonte === 'servidor' ? '' :
                                '';
                        mensagem += ` ${fonteTexto}`;
                    }
                    mostrarAvisoConflito(dataField, mensagem);
                }
                else {
                    // Fallback manual
                    const avisoConflito = document.createElement('div');
                    avisoConflito.className = 'alert alert-danger aviso-conflito mt-2';
                    let mensagem = `${data.mensagem || 'UH não disponível no período.'}`;
                    if (data.fonte) {
                        const fonteTexto = data.fonte === 'local' ? '' :
                            data.fonte === 'servidor' ? '' :
                                '';
                        mensagem += ` ${fonteTexto}`;
                    }
                    avisoConflito.innerHTML = mensagem;
                    (_a = dataField.parentNode) === null || _a === void 0 ? void 0 : _a.appendChild(avisoConflito);
                    // Destacar campos
                    dataField.classList.add('border-danger');
                    dataField.style.backgroundColor = '#fff5f5';
                }
            }
        }
    }
    // Funções de validação de datas removidas
    // O sistema já possui outro mecanismo que funciona melhor no momento do registro
    // Função de validação de datas removida
    // O sistema já possui outro mecanismo que funciona melhor no momento do registro
    salvarEdicaoReserva(form) {
        const formData = new FormData(form);
        // Adicionar flag AJAX para o servidor identificar o contexto
        formData.append('ajax', '1');
        // Adicionar ação de editar
        formData.append('acao', 'editar');
        // Log dos dados enviados
        console.log('Enviando dados para salvar:');
        // Usando type assertion para informar ao TypeScript que entries() existe
        for (let pair of formData.entries()) {
            console.log(pair[0] + ': ' + pair[1]);
        }
        fetch('reservas_salvar.php', {
            method: 'POST',
            body: formData
        })
            .then(response => {
            console.log('Status da resposta:', response.status);
            return response.text();
        })
            .then(text => {
            console.log('Resposta bruta do servidor:', text);
            if (!text.trim()) {
                throw new Error('Resposta vazia do servidor');
            }
            let data;
            try {
                data = JSON.parse(text);
                console.log('Dados JSON processados:', data);
            }
            catch (e) {
                console.error('Erro ao processar JSON:', e, 'Texto recebido:', text);
                // Não considerar como sucesso automaticamente
                throw new Error('Resposta inválida do servidor: ' + text);
            }
            if (data.success) {
                alert('Reserva editada com sucesso!');
                const modal = bootstrap.Modal.getInstance(document.getElementById('editarReservaModal'));
                if (modal) {
                    modal.hide();
                }
                setTimeout(() => location.reload(), 500);
            }
            else {
                alert('Erro ao editar reserva: ' + (data.message || 'Erro desconhecido'));
            }
        })
            .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao salvar: ' + error.message);
        });
    }
}
// Manter compatibilidade global
window.ModalEditarReserva = ModalEditarReserva;
// Adicionar exportação ES6
export { ModalEditarReserva };
