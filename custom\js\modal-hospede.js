// Importar função do func.ts
import { validarCPF } from './func.js';
// Módulo para gerenciar busca e cadastro de hóspedes
class ModalHospede {
    constructor() {
        this.selecionarHospedeModal = document.getElementById('selecionarHospedeModal');
        this.init();
    }
    init() {
        this.setupEventListeners();
    }
    setupEventListeners() {
        const btnBuscarHospede = document.getElementById('btnBuscarHospede');
        const buscarHospedeInput = document.getElementById('buscarHospede');
        const btnNovoHospede = document.getElementById('btnNovoHospede');
        if (btnBuscarHospede) {
            btnBuscarHospede.addEventListener('click', () => this.buscarHospedes());
        }
        if (buscarHospedeInput) {
            buscarHospedeInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    this.buscarHospedes();
                }
            });
        }
        if (btnNovoHospede) {
            btnNovoHospede.addEventListener('click', () => this.handleNovoHospede());
        }
    }
    buscarHospedes() {
        const buscarHospedeElement = document.getElementById('buscarHospede');
        const termoBusca = buscarHospedeElement.value.trim();
        const resultadosBusca = document.getElementById('resultadosBusca');
        if (!resultadosBusca)
            return;
        // Permitir busca sem limitação de caracteres (busca vazia retorna todos)
        // if (termoBusca.length < 2) {
        //     resultadosBusca.innerHTML = '<div class="alert alert-warning">Digite pelo menos 2 caracteres para buscar.</div>';
        //     return;
        // }
        resultadosBusca.innerHTML = '<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Carregando...</span></div></div>';
        fetch(`buscar_hospedes.php?termo=${encodeURIComponent(termoBusca)}`, {
            method: 'GET'
        })
            .then(response => response.json())
            .then((data) => {
            if (data && data.length > 0) {
                let html = '<div class="list-group">';
                data.forEach(hospede => {
                    html += `
                        <a href="#" class="list-group-item list-group-item-action hospede-item" 
                           data-id="${hospede.id}" data-nome="${hospede.nome}">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">${hospede.nome}</h6>
                                <small>ID: ${hospede.id}</small>
                            </div>
                            <p class="mb-1">CPF: ${hospede.documento || 'Não informado'}</p>
                            <small>Cidade: ${hospede.cidade || 'Não informado'} - ${hospede.uf || ''}</small>
                        </a>`;
                });
                html += '</div>';
                resultadosBusca.innerHTML = html;
                // Adicionar eventos de clique aos itens
                const items = resultadosBusca.querySelectorAll('.hospede-item');
                items.forEach(item => {
                    item.addEventListener('click', (e) => {
                        e.preventDefault();
                        const hospedeId = item.getAttribute('data-id');
                        const hospedeNome = item.getAttribute('data-nome');
                        const uhElement = document.getElementById('uhSelecionada');
                        const dataElement = document.getElementById('dataSelecionada');
                        const uh = uhElement.value;
                        const data = dataElement.value;
                        // Fechar modal de seleção
                        const modalSelecaoHospede = bootstrap.Modal.getInstance(this.selecionarHospedeModal);
                        if (modalSelecaoHospede)
                            modalSelecaoHospede.hide();
                        // Disparar evento customizado para comunicação entre módulos
                        document.dispatchEvent(new CustomEvent('hospedeSelected', {
                            detail: { hospede: { id: hospedeId, nome: hospedeNome }, uh, data }
                        }));
                    });
                });
            }
            else {
                resultadosBusca.innerHTML = '<div class="alert alert-info">Nenhum hóspede encontrado.</div>';
            }
        })
            .catch(error => {
            if (resultadosBusca) {
                resultadosBusca.innerHTML = '<div class="alert alert-danger">Erro ao buscar hóspedes. Tente novamente.</div>';
            }
            console.error('Erro na busca:', error);
        });
    }
    handleNovoHospede() {
        const uhElement = document.getElementById('uhSelecionada');
        const dataElement = document.getElementById('dataSelecionada');
        const uh = uhElement.value;
        const data = dataElement.value;
        // Fechar modal de seleção
        const modalSelecaoHospede = bootstrap.Modal.getInstance(this.selecionarHospedeModal);
        if (modalSelecaoHospede)
            modalSelecaoHospede.hide();
        // Carregar formulário completo via AJAX
        setTimeout(() => {
            this.carregarFormularioCompleto(uh, data);
        }, 500);
    }
    carregarFormularioCompleto(uh, data) {
        const modalCompletoElement = document.getElementById('cadastroCompletoHospedeModal');
        if (!modalCompletoElement)
            return;
        const modalCompleto = new bootstrap.Modal(modalCompletoElement);
        const container = document.getElementById('formularioCompletoContainer');
        if (!container)
            return;
        // Mostrar modal com loading
        modalCompleto.show();
        // Fazer requisição AJAX
        fetch('carregar_formulario_hospede.php')
            .then(response => response.json())
            .then((responseData) => {
            if (responseData.success && responseData.html) {
                container.innerHTML = responseData.html;
                // Aguardar um pouco para o HTML ser renderizado
                setTimeout(() => {
                    var _a, _b;
                    // Verificar se os elementos existem antes de acessar
                    const uhField = document.getElementById('uh_reserva_hidden');
                    const dataField = document.getElementById('data_reserva_hidden');
                    if (uhField) {
                        uhField.value = uh;
                        console.log('UH definida:', uh);
                    }
                    else {
                        console.error('Campo uh_reserva_hidden não encontrado');
                    }
                    if (dataField) {
                        dataField.value = data;
                        console.log('Data definida:', data);
                    }
                    else {
                        console.error('Campo data_reserva_hidden não encontrado');
                    }
                    // Verificar se o script func.js já está carregado
                    const scriptJaCarregado = Array.from(document.scripts).some(script => script.src.includes('custom/js/func.js'));
                    if (!scriptJaCarregado) {
                        // Carregar script func.js para funcionalidades do formulário
                        const script = document.createElement('script');
                        script.src = 'custom/js/func.js';
                        document.head.appendChild(script);
                        console.log('Script func.js carregado dinamicamente');
                    }
                    else {
                        console.log('Script func.js já está carregado, não será incluído novamente');
                        // Inicializar manualmente as funções necessárias
                        // Inicializar o cálculo de idade
                        const nascElement = document.getElementsByName('nasc')[0];
                        if (nascElement) {
                            // Remover event listeners existentes para evitar duplicação
                            const novoNascElement = nascElement.cloneNode(true);
                            (_a = nascElement.parentNode) === null || _a === void 0 ? void 0 : _a.replaceChild(novoNascElement, nascElement);
                            // Adicionar novo event listener
                            novoNascElement.addEventListener('change', function () {
                                const dataNascimento = new Date(this.value);
                                const hoje = new Date();
                                let idade = hoje.getFullYear() - dataNascimento.getFullYear();
                                const mes = hoje.getMonth() - dataNascimento.getMonth();
                                if (mes < 0 || (mes === 0 && hoje.getDate() < dataNascimento.getDate())) {
                                    idade--;
                                }
                                const idadeInput = document.getElementsByName('idade')[0];
                                if (idadeInput) {
                                    idadeInput.value = idade.toString();
                                }
                            });
                        }
                        // Inicializar formatação e validação de CPF
                        const cpfElement = document.getElementsByName('cpf')[0];
                        if (cpfElement) {
                            // Remover event listeners existentes para evitar duplicação
                            const novoCpfElement = cpfElement.cloneNode(true);
                            (_b = cpfElement.parentNode) === null || _b === void 0 ? void 0 : _b.replaceChild(novoCpfElement, cpfElement);
                            // Adicionar event listeners para formatação e validação
                            novoCpfElement.addEventListener('input', function () {
                                let cpf = this.value.replace(/\D/g, ''); // Remove caracteres não numéricos
                                if (cpf.length <= 11) {
                                    cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                                    cpf = cpf.replace(/(\d{3})(\d)/, '$1.$2');
                                    cpf = cpf.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
                                }
                                this.value = cpf;
                                const feedbackDiv = document.getElementById('cpf-feedback');
                                const submitButton = document.querySelector('button[type="submit"]');
                                // Verifica se o CPF está incompleto
                                if (cpf.length < 14 && cpf.length > 0) {
                                    this.classList.remove('is-valid');
                                    this.classList.add('is-invalid');
                                    this.title = 'CPF incompleto. Por favor, digite todos os dígitos.';
                                    // CPF incompleto - desabilitar botão
                                    if (feedbackDiv) {
                                        feedbackDiv.innerHTML = '<b>CPF incompleto</b>. Digite todos os dígitos.';
                                        feedbackDiv.style.color = 'orange';
                                    }
                                    if (submitButton) {
                                        submitButton.disabled = true;
                                        submitButton.textContent = 'CPF Incompleto';
                                        submitButton.classList.add('btn-secondary');
                                        submitButton.classList.remove('btn-primary', 'btn-danger');
                                    }
                                }
                                else if (cpf.length === 14) { // Verifica se o CPF está completo
                                    if (validarCPF(cpf)) {
                                        this.classList.remove('is-invalid');
                                        this.classList.add('is-valid');
                                        this.title = '';
                                        // CPF válido, mas ainda precisa verificar duplicidade
                                        if (feedbackDiv) {
                                            feedbackDiv.innerHTML = '<b>Verificando disponibilidade...</b>';
                                            feedbackDiv.style.color = 'orange';
                                        }
                                        // Verificar duplicidade
                                        verificarCPFDuplicado(cpf, this);
                                    }
                                    else {
                                        this.classList.remove('is-valid');
                                        this.classList.add('is-invalid');
                                        this.title = 'CPF inválido. Verifique a digitação.';
                                        // CPF inválido - desabilitar botão
                                        if (feedbackDiv) {
                                            feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
                                            feedbackDiv.style.color = 'red';
                                        }
                                        if (submitButton) {
                                            submitButton.disabled = true;
                                            submitButton.textContent = 'CPF Inválido';
                                            submitButton.classList.add('btn-secondary');
                                            submitButton.classList.remove('btn-primary', 'btn-danger');
                                        }
                                    }
                                }
                                else if (cpf.length === 0) {
                                    // Campo vazio - remover classes de validação
                                    this.classList.remove('is-valid', 'is-invalid');
                                    this.title = '';
                                    if (feedbackDiv) {
                                        feedbackDiv.innerHTML = '';
                                    }
                                    // Permitir envio se outros campos obrigatórios estiverem preenchidos
                                    if (submitButton) {
                                        submitButton.disabled = false;
                                        submitButton.textContent = 'Salvar';
                                        submitButton.classList.remove('btn-secondary', 'btn-danger');
                                        submitButton.classList.add('btn-primary');
                                    }
                                }
                            });
                        }
                        // Inicializar outras funções se necessário
                        if (typeof window.setupEnterAsTab === 'function') {
                            window.setupEnterAsTab();
                        }
                    }
                    // Adicionar evento de submit ao formulário
                    const form = document.getElementById('formHospedeCompleto');
                    if (form) {
                        form.addEventListener('submit', (e) => {
                            e.preventDefault();
                            this.salvarHospedeCompleto(form);
                        });
                        console.log('Event listener do form adicionado');
                    }
                    else {
                        console.error('Formulário formHospedeCompleto não encontrado');
                    }
                }, 100);
            }
            else {
                container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
            }
        })
            .catch(error => {
            console.error('Erro:', error);
            if (container) {
                container.innerHTML = '<div class="alert alert-danger">Erro ao carregar formulário.</div>';
            }
        });
    }
    salvarHospedeCompleto(form) {
        const formData = new FormData(form);
        // Log dos dados sendo enviados
        console.log('Enviando dados:');
        for (let [key, value] of formData.entries()) {
            console.log(`${key}: ${value}`);
        }
        fetch('hospedes_salvar.php', {
            method: 'POST',
            body: formData
        })
            .then(response => {
            console.log('Response status:', response.status);
            return response.text(); // Primeiro como texto para debug
        })
            .then(text => {
            console.log('Response text:', text);
            // Verificar se a resposta está vazia
            if (!text.trim()) {
                throw new Error('Resposta vazia do servidor');
            }
            // Tentar fazer parse do JSON
            let data;
            try {
                data = JSON.parse(text);
            }
            catch (e) {
                console.error('Resposta não é JSON válido:', text);
                throw new Error('Resposta inválida do servidor');
            }
            const responseData = data;
            if (responseData.success) {
                // Fechar modal de cadastro de hóspede
                const modalElement = document.getElementById('cadastroCompletoHospedeModal');
                if (modalElement) {
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal)
                        modal.hide();
                }
                // Abrir modal de reserva ao invés de redirecionar
                setTimeout(() => {
                    console.log('Tentando abrir modal de reserva com:', responseData);
                    // Verificar se modalReserva está disponível no objeto window
                    if (typeof window.modalReserva === 'undefined') {
                        console.log('window.modalReserva não está definido, tentando obter da instância mapaUHMain');
                        if (window.mapaUHMain && window.mapaUHMain.getModalReserva()) {
                            window.modalReserva = window.mapaUHMain.getModalReserva();
                        }
                    }
                    // Tentar abrir o modal de reserva
                    if (window.modalReserva && responseData.uh && responseData.data_reserva &&
                        responseData.hospede_id && responseData.hospede_nome) {
                        console.log('Abrindo modal de reserva com:', {
                            uh: responseData.uh,
                            data: responseData.data_reserva,
                            hospede_id: responseData.hospede_id,
                            hospede_nome: responseData.hospede_nome
                        });
                        window.modalReserva.carregarFormularioReserva(responseData.uh, responseData.data_reserva, responseData.hospede_id, responseData.hospede_nome);
                    }
                    else {
                        // Alternativa: usar a função global
                        console.log('Tentando usar função global carregarFormularioReserva');
                        if (typeof window.carregarFormularioReserva === 'function') {
                            window.carregarFormularioReserva(responseData.uh || '', responseData.data_reserva || '', {
                                id: responseData.hospede_id || '',
                                nome: responseData.hospede_nome || ''
                            });
                        }
                        else {
                            console.error('Não foi possível abrir o modal de reserva. Detalhes:', {
                                modalReservaExiste: !!window.modalReserva,
                                funcaoGlobalExiste: typeof window.carregarFormularioReserva === 'function',
                                dados: responseData
                            });
                            alert('Hóspede cadastrado com sucesso! Recarregue a página para continuar.');
                        }
                    }
                }, 500);
            }
            else {
                alert('Erro ao salvar hóspede: ' + (responseData.message || 'Erro desconhecido'));
            }
        })
            .catch(error => {
            console.error('Erro:', error);
            alert('Erro ao processar a requisição: ' + error.message);
        });
    }
    abrirModalSelecaoHospede(uh, data) {
        // Armazenar UH e data para uso posterior
        const uhElement = document.getElementById('uhSelecionada');
        const dataElement = document.getElementById('dataSelecionada');
        if (uhElement)
            uhElement.value = uh;
        if (dataElement)
            dataElement.value = data;
        // Limpar resultados anteriores
        const resultadosBusca = document.getElementById('resultadosBusca');
        if (resultadosBusca) {
            resultadosBusca.innerHTML = '<div class="alert alert-info">Digite o nome ou CPF do hóspede para buscar.</div>';
        }
        // Limpar campo de busca
        const buscarInput = document.getElementById('buscarHospede');
        if (buscarInput) {
            buscarInput.value = '';
        }
        // Abrir o modal
        const modal = new bootstrap.Modal(this.selecionarHospedeModal);
        modal.show();
    }
}
// Manter compatibilidade global
window.ModalHospede = ModalHospede;
// Função para verificar CPF duplicado
function verificarCPFDuplicado(cpf, inputElement) {
    const submitButton = document.querySelector('button[type="submit"]');
    const feedbackDiv = document.getElementById('cpf-feedback');
    // Só verificar duplicidade se CPF for válido e tiver 11 dígitos
    if (cpf.length < 11) {
        if (feedbackDiv) {
            feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
        }
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'CPF Incompleto';
            submitButton.classList.add('btn-secondary');
            submitButton.classList.remove('btn-primary');
        }
        return;
    }
    if (!validarCPF(cpf)) {
        if (feedbackDiv) {
            feedbackDiv.innerHTML = '<b>CPF inválido</b>.';
        }
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.textContent = 'CPF Inválido';
            submitButton.classList.add('btn-secondary');
            submitButton.classList.remove('btn-primary');
        }
        return;
    }
    // Construir URL da requisição
    let url = 'verificar_cpf.php?cpf=' + encodeURIComponent(cpf);
    fetch(url)
        .then(response => response.json())
        .then((data) => {
        if (data.existe) {
            // CPF duplicado - desabilitar botão e mostrar erro
            inputElement.classList.remove('is-valid');
            inputElement.classList.add('is-invalid');
            inputElement.title = 'CPF já cadastrado para: ' + (data.nome_hospede || '');
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF já cadastrado para: ' + (data.nome_hospede || '') + '</b>';
                feedbackDiv.style.color = 'red';
            }
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.textContent = 'CPF Duplicado - Não é possível registrar';
                submitButton.classList.add('btn-danger');
                submitButton.classList.remove('btn-primary', 'btn-secondary');
            }
        }
        else {
            // CPF disponível - habilitar botão
            inputElement.classList.remove('is-invalid');
            inputElement.classList.add('is-valid');
            inputElement.title = '';
            if (feedbackDiv) {
                feedbackDiv.innerHTML = '<b>CPF disponível</b>';
                feedbackDiv.style.color = 'green';
            }
            if (submitButton) {
                submitButton.disabled = false;
                submitButton.textContent = 'Registrar';
                submitButton.classList.add('btn-primary');
                submitButton.classList.remove('btn-danger', 'btn-secondary');
            }
        }
    })
        .catch(error => {
        console.error('Erro ao verificar CPF:', error);
        // Em caso de erro, permitir o envio (fallback)
        if (submitButton) {
            submitButton.disabled = false;
            submitButton.textContent = 'Registrar';
            submitButton.classList.add('btn-primary');
            submitButton.classList.remove('btn-danger', 'btn-secondary');
        }
    });
}
// Remover esta linha:
// window.ModalHospede = ModalHospede;
// Exportar como módulo ES6 (remover validarCPF da exportação)
export { ModalHospede };
