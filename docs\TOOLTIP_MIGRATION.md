# Migração para Sistema de Tooltips Unificado

## Visão Geral

Este documento descreve a migração das três principais mensagens do sistema para um sistema unificado de tooltips Bootstrap, proporcionando uma interface mais limpa e consistente.

## Mensagens Migradas

### 1. "Reservada em" (Conflitos de Reserva)
**Antes:** Popover Bootstrap manual
**Depois:** Tooltip de conflito com estilo customizado

### 2. "Data/hora de entrada deve ser anterior à saída"
**Antes:** Mensagem de erro retornada pela API
**Depois:** Tooltip de validação

### 3. "Data Antiga"
**Antes:** CSS pseudo-element (::before) com tarja amarela
**Depois:** Tooltip de aviso no hover

## Arquivos Criados/Modificados

### Novos Arquivos
- `custom/src/js/tooltip-manager.ts` - Gerenciador unificado de tooltips
- `custom/css/tooltip-system.css` - Estilos customizados para tooltips
- `docs/TOOLTIP_MIGRATION.md` - Esta documentação

### Arquivos Modificados
- `custom/src/js/modal-reserva.ts` - Integração com tooltip manager
- `custom/src/js/valida_data_anterior.ts` - Substituição de CSS por tooltips

## Como Usar o Novo Sistema

### Importação
```typescript
import { tooltipManager } from './tooltip-manager.js';
```

### Métodos Disponíveis

#### 1. Tooltip de Conflito
```typescript
tooltipManager.showConflictTooltip(element, "Reservada em 25/06 de 13:00 às 15:00");
```

#### 2. Tooltip de Validação
```typescript
tooltipManager.showValidationTooltip(element, "Data/hora de entrada deve ser anterior à saída");
```

#### 3. Tooltip de Data Antiga
```typescript
tooltipManager.showOldDateTooltip(element);
```

#### 4. Remover Tooltip
```typescript
tooltipManager.hideTooltip(element);
```

#### 5. Remover Todos os Tooltips
```typescript
tooltipManager.hideAllTooltips();
```

## Configuração Avançada

### Tooltip Customizado
```typescript
tooltipManager.showTooltip({
    element: inputElement,
    message: "Sua mensagem aqui",
    type: 'error' | 'warning' | 'info' | 'conflict',
    trigger: 'hover' | 'focus' | 'manual' | 'click',
    placement: 'top' | 'bottom' | 'left' | 'right' | 'auto',
    autoHide: true,
    hideDelay: 5000
});
```

## Tipos de Tooltip

### 1. Error (Vermelho)
- Usado para erros de validação
- Cor: #dc3545
- Auto-hide: 6 segundos
- Trigger: manual

### 2. Warning (Amarelo)
- Usado para avisos como "Data Antiga"
- Cor: #ffc107
- Auto-hide: não
- Trigger: hover

### 3. Conflict (Vermelho Escuro)
- Usado para conflitos de reserva
- Cor: #dc3545 com borda
- Auto-hide: 8 segundos
- Trigger: manual

### 4. Info (Azul)
- Usado para informações gerais
- Cor: #0dcaf0
- Auto-hide: configurável
- Trigger: configurável

## Estilos CSS Aplicados

### Campos com Tooltips Ativos
- `.field-with-error-tooltip` - Borda vermelha com sombra
- `.field-with-warning-tooltip` - Borda amarela com sombra
- `.field-with-conflict-tooltip` - Borda vermelha com fundo levemente colorido

### Responsividade
- Tooltips se adaptam automaticamente a telas pequenas
- Tamanho de fonte e padding reduzidos em dispositivos móveis

### Acessibilidade
- Suporte a temas escuros
- Alto contraste para usuários com deficiência visual
- Redução de animações para usuários sensíveis a movimento

## Vantagens do Novo Sistema

### 1. Interface Mais Limpa
- Remove elementos visuais permanentes
- Informações aparecem apenas quando necessário
- Reduz poluição visual da interface

### 2. Consistência
- Todos os tipos de feedback usam o mesmo sistema
- Estilos padronizados e coerentes
- Comportamento previsível

### 3. Melhor UX
- Tooltips não interferem no layout
- Posicionamento automático inteligente
- Auto-hide configurável

### 4. Responsividade
- Adaptação automática a diferentes tamanhos de tela
- Melhor experiência em dispositivos móveis

### 5. Acessibilidade
- Suporte nativo do Bootstrap para screen readers
- Compatibilidade com preferências do sistema
- Navegação por teclado

## Compatibilidade

### Navegadores Suportados
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### Dependências
- Bootstrap 5.3.3 (já presente no projeto)
- TypeScript (para desenvolvimento)

## Migração Gradual

O sistema foi projetado para coexistir com as implementações antigas durante a transição:

1. **Fase 1:** Implementação do tooltip manager ✅
2. **Fase 2:** Migração de conflitos de reserva ✅
3. **Fase 3:** Migração de validação de datas ✅
4. **Fase 4:** Migração de "Data Antiga" ✅
5. **Fase 5:** Remoção de código legado (próxima etapa)

## Próximos Passos

### 1. Incluir CSS no Layout Principal
Adicionar ao `<head>` das páginas:
```html
<link rel="stylesheet" href="custom/css/tooltip-system.css">
```

### 2. Compilar TypeScript
```bash
tsc custom/src/js/tooltip-manager.ts --target es2017 --module es6
```

### 3. Testar em Diferentes Cenários
- Formulários de reserva
- Validação de datas
- Conflitos de disponibilidade
- Dispositivos móveis

### 4. Remover Código Legado
- CSS antigo de `.data-anterior-hoje`
- Implementações de popover manual
- Elementos DOM de avisos inline

## Troubleshooting

### Tooltip Não Aparece
1. Verificar se o Bootstrap está carregado
2. Confirmar importação do tooltip-manager
3. Verificar console para erros JavaScript

### Estilos Não Aplicados
1. Verificar se tooltip-system.css está incluído
2. Confirmar ordem de carregamento dos CSS
3. Verificar especificidade CSS

### Performance
1. Usar `hideAllTooltips()` ao trocar de página
2. Evitar criar muitos tooltips simultâneos
3. Configurar auto-hide apropriadamente

## Suporte

Para dúvidas ou problemas com o novo sistema de tooltips, consulte:
1. Esta documentação
2. Código fonte em `custom/src/js/tooltip-manager.ts`
3. Estilos em `custom/css/tooltip-system.css`
