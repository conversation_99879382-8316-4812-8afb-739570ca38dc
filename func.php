<?php
		include_once 'config.php'; // Inclua a conexão com o banco de dados

		// Verificação de função existente (pode remover depois)
		error_log("Verificando função prepareAndExecute:");
		if (function_exists('prepareAndExecute')) {
			error_log("Função prepareAndExecute existe");
		} else {
			error_log("Função prepareAndExecute NÃO existe!");
		}


		function prepareAndExecute($conn, $query, $types, ...$params) {
			$stmt = $conn->prepare($query);
			if ($stmt === false) {
				die('Prepare failed: ' . htmlspecialchars($conn->error));
			}
			$stmt->bind_param($types, ...$params);
			if ($stmt->execute() === false) {
				die('Execute failed: ' . htmlspecialchars($stmt->error));
			}
			return $stmt;
		}

		// Verificar se o usuário tem permissões de administrador
		function isAdmin($conn, $userId) {
			$stmt = prepareAndExecute($conn, "SELECT is_admin FROM usuarios WHERE id = ?", 'i', $userId);
			$result = $stmt->get_result();
			if ($result->num_rows > 0) {
				$row = $result->fetch_assoc();
				return $row['is_admin'];
			}
			return false;
		}

	/**
		* Inicializa dados padrão para uma nova pousada
		* Copia formas de pagamento e categorias financeiras dos templates (pousada_id = 0)
		*/
	function inicializarDadosPadraoNovaPousada($conn, $pousada_id) {
		try {
			// Verificar se já possui formas de pagamento
			$stmt = $conn->prepare("SELECT COUNT(*) as total FROM formas_pagamento WHERE pousada_id = ?");
			$stmt->bind_param("i", $pousada_id);
			$stmt->execute();
			$result = $stmt->get_result();
			$tem_formas = $result->fetch_assoc()['total'] > 0;
		
			// Se não tem formas de pagamento, copiar dos templates
			if (!$tem_formas) {
				$sql_copy_formas = "
					INSERT INTO formas_pagamento (pousada_id, nome, descricao, afeta_caixa, is_active)
					SELECT ?, nome, descricao, afeta_caixa, is_active 
					FROM formas_pagamento 
					WHERE pousada_id = 0
				";
				$stmt = $conn->prepare($sql_copy_formas);
				$stmt->bind_param("i", $pousada_id);
				$stmt->execute();
			
				error_log("Formas de pagamento padrão copiadas para pousada_id: $pousada_id");
			}
		
			// Verificar se já possui categorias financeiras
			$stmt = $conn->prepare("SELECT COUNT(*) as total FROM categorias_financeiras WHERE pousada_id = ?");
			$stmt->bind_param("i", $pousada_id);
			$stmt->execute();
			$result = $stmt->get_result();
			$tem_categorias = $result->fetch_assoc()['total'] > 0;
		
			// Se não tem categorias, copiar dos templates
			if (!$tem_categorias) {
				$sql_copy_categorias = "
					INSERT INTO categorias_financeiras (pousada_id, nome, tipo, descricao, cor, is_default)
					SELECT ?, nome, tipo, descricao, cor, is_default 
					FROM categorias_financeiras 
					WHERE pousada_id = 0
				";
				$stmt = $conn->prepare($sql_copy_categorias);
				$stmt->bind_param("i", $pousada_id);
				$stmt->execute();
			
				error_log("Categorias financeiras padrão copiadas para pousada_id: $pousada_id");
			}
		
			return true;
		
		} catch (Exception $e) {
			error_log("Erro ao inicializar dados padrão para pousada $pousada_id: " . $e->getMessage());
			return false;
		}
	}

	/**
		* Verifica se é necessário inicializar dados padrão e executa
		*/
	function verificarEInicializarDadosPadrao($conn, $pousada_id) {
		// Verificar se a pousada precisa de inicialização
		$stmt = $conn->prepare("
			SELECT 
				(SELECT COUNT(*) FROM formas_pagamento WHERE pousada_id = ?) as formas_count,
				(SELECT COUNT(*) FROM categorias_financeiras WHERE pousada_id = ?) as categorias_count
		");
		$stmt->bind_param("ii", $pousada_id, $pousada_id);
		$stmt->execute();
		$result = $stmt->get_result();
		$counts = $result->fetch_assoc();
	
		// Se não tem dados, inicializar
		if ($counts['formas_count'] == 0 || $counts['categorias_count'] == 0) {
			return inicializarDadosPadraoNovaPousada($conn, $pousada_id);
		}
	
		return true;
	}
	// Verificar se o usuário é o super administrador (usuário 0)
	function isSuperAdmin($conn, $userId) {
		$stmt = prepareAndExecute($conn, "SELECT pousada_id FROM usuarios WHERE id = ?", 'i', $userId);
		$result = $stmt->get_result();
		if ($result->num_rows > 0) {
			$row = $result->fetch_assoc();
			return $row['pousada_id'] == 0;
		}
		return false;
	}
	?>

