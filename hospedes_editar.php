<?php

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
	include_once("config.php");

// Verifica se o usuário está logado e se as variáveis de sessão estão setadas
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
    $is_admin = $_SESSION['user_is_admin'];
} else {
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}

    $hospede_id = $_GET["hospede_id"];
	$sql = "SELECT * FROM hospedes WHERE id='$hospede_id' and pousada_id='$pousada_id'";
	$res = $conn->query($sql);
	$row = $res->fetch_object(); 
?>

<center>
	
	<div class="text-center mb-3 print-only">
		<img src="<?php echo $_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png'; ?>" alt="Logo" class="print-logo">
	</div>

	<div class="form-group no-print">
		<button onclick="location.href='index.php';" class="btn btn-success">Home</button>
		<button type="button" class="btn btn-secondary" id="print-button-top">Imprimir</button>
	</div>
</center>

<?php
// Definir variáveis para o formulário preenchido
$form_action = '?page=salvar';
$hidden_fields = '<input type="hidden" name="acao" value="editar"><input type="hidden" name="id" value="' . $row->id . '">';
$form_id = 'editarHospede';

// Preencher valores dos campos
$nome = $row->nome;
$nasc = $row->nasc;
$idade = $row->idade;
$profissao = $row->profissao;
$nacionalidade = $row->nacionalidade;
$sexo = $row->sexo;
$cpf = $row->cpf;
$documento = $row->documento;
$tipo = $row->tipo;
$expedidor = $row->expedidor;
$endereco = $row->endereco;
$telefone = $row->telefone;
$cep = $row->cep;
$cidade = $row->cidade;
$uf = $row->uf;
$pais = $row->pais;
$email = $row->email;

$show_hospede_info = true;
$titulo_hospede = "Editar Hospede";

// Incluir o formulário reutilizável
include 'formulario_hospede.php';
?>

<center>
<br>
<div id="qrcode"></div>
<br>
<div class="form-group no-print">
	<button type="button" class="btn btn-secondary" id="print-button-bottom">Imprimir</button>
</div>
</center>

<script type="text/javascript" src="instascan/qrcode.js"></script>
<script src="custom/js/func.js"></script>

<script>
// Adiciona eventos de clique aos botões de impressão
document.getElementById("print-button-top").addEventListener("click", imprimir);
document.getElementById("print-button-bottom").addEventListener("click", imprimir); 

function createQrCode(id) {
	var qrcode = new QRCode("qrcode", {
		text: id,
		width: 80,
		height: 80,
		colorDark: "black",
		colorLight: "white",
		correctLevel : QRCode.CorrectLevel.H
	});
}

// Chama a função para criar o QR Code passando o ID diretamente
createQrCode("<?php echo $row->id; ?>");

function imprimir() {
	window.print();
}
</script>
