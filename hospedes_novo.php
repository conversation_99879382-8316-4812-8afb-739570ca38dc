<?php
//error_reporting(E_ALL);
//ini_set('display_errors', 1);

// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Verificar se há dados preservados de erro
$form_data = [];
$error_message = '';
if (isset($_SESSION['form_data'])) {
    $form_data = $_SESSION['form_data'];
    unset($_SESSION['form_data']);
}
if (isset($_SESSION['error_message'])) {
    $error_message = $_SESSION['error_message'];
    unset($_SESSION['error_message']);
}

if (!isset($_SESSION['user_id'])) {
	echo "<script>window.location.href = 'index.html';</script>";
	return;
}
?>

<center>
<?php
if (!empty($error_message)) {
    echo '<div class="alert alert-danger" role="alert">';
    echo '<strong>Erro:</strong> ' . htmlspecialchars($error_message);
    echo '</div>';
}
	echo '<br>';
	echo '<button onclick="location.href=\'index.php\';" class="btn btn-success">Home</button>';
?>
</center>

<?php
// Definir variáveis para o formulário
$form_action = '?page=salvar';
$hidden_fields = '<input type="hidden" name="acao" value="cadastrar">';
$form_id = 'registroHospedes';

$titulo_hospede="Novo Hóspede";

// Incluir o formulário reutilizável
include 'formulario_hospede.php';
?>

<?php 
//Precisa ficar fora do form, se não gera uma inserção vazia no banco
echo '<center><button onclick="location.href=\'index.php\'" class="btn btn-cancelar">Cancelar</button></center>'; 
?>

<script src="custom/js/func.js"></script>
