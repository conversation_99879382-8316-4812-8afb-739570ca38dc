<?php

//error_reporting(E_ALL);
//ini_set('display_errors', 1);

// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

if (!isset($_SESSION['user_id'])) {
    echo "<script>window.location.href = 'index.html';</script>";
    return;
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1">
	
	<!-- Bootstrap CSS -->
	<link href="css/bootstrap.min.css" rel="stylesheet">
	<link rel="stylesheet" href="custom/css/form_fnrh.css">
	<link rel="stylesheet" href="custom/css/admin-financeiro.css">
    <title>Procurar Hospedes</title>
</head>
<body class="hospedes-procurar">
    <center>
    <h1>Procurar Hospedes</h1>

    <form action="hospedes_listar.php" method="post">
        <table>
            <tr>
                <td><label for="tipo_pesquisa">Procurar por:</label></td>
                <td>
                    <select class="form-control" name="tipo_pesquisa" id="tipo_pesquisa">
                        <option value="nome">Nome</option>
                        <option value="profissao">Profissão</option>
                        <option value="cidade">Cidade</option>
						<option value="uf">Estado</option>
                        <option value="cpf">CPF</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td><label for="termo_pesquisa">Termo de pesquisa:</label></td>
                <td><input class="form-control" type="text" name="termo_pesquisa" id="termo_pesquisa" value="<?php echo isset($_GET['nome']) ? htmlspecialchars($_GET['nome']) : ''; ?>" required></td>
            </tr>
        </table>
		<center>
            <td colspan="2"><button class="btn btn-primary" type="submit">Procurar</button></td>
		</center>
    </form>
	
	<?php
    echo '<br>';
    echo '<button onclick="location.href=\'index.php\';" class="btn btn-success">Home</button>';
	?>
	</center>
	<script src="js/bootstrap.bundle.min.js"></script>
	
</body>
</html>
