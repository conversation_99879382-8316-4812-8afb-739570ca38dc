<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HospedaMax Sistema de Cadastro de Hóspedes</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="custom/css/externo.css">
</head>
<body>
        <div class="header-container">
            <!-- Área do topo com o logo -->
            <div class="topo-area">
                <h1><img src="/img/logo_off.gif" 
                    onMouseOver="this.src='/img/logo_on.gif'"  
                    onMouseOut="this.src='/img/logo_off.gif'"  
                    onClick="location='https://www.claudiosegura.com/index.php'" ></h1>
            </div>
            
            <!-- <PERSON>rea do banner com fundo -->
            <div class="banner-area">
                <div class="header-title">
                    <h1>HospedaMax</h1>
                    <h1>Sistema de Cadastro de Hóspedes</h1>
                </div>
            </div>
        </div>

        <div class="header-menu">
            <a href="#" class="menu-item" data-bs-toggle="modal" data-bs-target="#loginModal">Entrar</a>
            <a href="contratar.php" class="menu-item">Contratar</a>
        </div>

        <!-- Container Principal -->
        <div class="main-container">
            <!-- Coluna de Conteúdo Principal -->
            <div class="content-column">
                <div class="advantage-section">
                    <h2>Vantagens do Sistema</h2>
                    <p>Nosso sistema de pousadas oferece funcionalidades essenciais para a gestão eficiente do seu negócio:</p>
                    <ul>
                        Cadastrar reservas de forma simples e rápida.
                        Gerar PDFs com resumo e QRCode para check-in ágil.
                        Opção de check-in online para hóspedes.
                        Cadastro completo conforme o modelo nacional de hotelaria.
                    </ul>
                    <p>Aumente a produtividade da sua pousada com um sistema integrado e fácil de usar.</p>
    
            </div>

            <div class="plans-section">
                <h2>Escolha Seu Plano</h2>
                <p>Oferecemos três opções de planos para atender às suas necessidades:</p>
                <ul>
                    <strong>Platina</strong> - Até 2 usuários
                    <strong>Esmeralda</strong> - Até 4 usuários
                    <strong>Dourado</strong> - Até 6 usuários
                </ul>
                <a href="contratar.php" class="btn btn-success btn-plan">Contratar Agora</a>
            </div>

            <!-- Coluna de Casos de Sucesso -->
            <div class="success-stories-column">
                <div class="success-stories">
                    <h2>Casos de Sucesso</h2>
                    <p>Veja como nossos clientes estão alcançando resultados incríveis com o sistema:</p>
                    <blockquote class="blockquote">
                        "Desde que começamos a usar o sistema, nossa eficiência aumentou significativamente e o processo de check-in é muito mais rápido!" - João, Pousada Sol Nascente
                    </blockquote>
                    <blockquote class="blockquote">
                        "A geração de PDFs com QR Code facilitou muito a recepção dos hóspedes. Recomendo!" - Maria, Pousada Lua Cheia
                    </blockquote>
                </div>
            </div>
        </div>

        <!-- Login Modal -->
        <div class="modal fade" id="loginModal" tabindex="-1" aria-labelledby="loginModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="loginModalLabel">Login</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <form method="POST" action="login.php">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email</label>
                                <input type="email" id="email" name="email" class="form-control" required>
                            </div>
                            <div class="mb-3">
                                <label for="senha" class="form-label">Senha</label>
                                <input type="password" id="senha" name="senha" class="form-control" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Login</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <script src="js/bootstrap.bundle.min.js"></script>

</body>
</html>
