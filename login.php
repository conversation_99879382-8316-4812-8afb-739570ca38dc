<?php
// Verificar se a sessão já está ativa antes de iniciar
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

include_once("config.php");
include_once("func.php");

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $email = trim($_POST['email'] ?? '');
    $senha = $_POST['senha'] ?? '';

    error_log("Login attempt: email='$email'");

    $sql = "SELECT u.id, u.nome, u.email, u.senha, u.pousada_id, u.is_admin,
                   p.logotipo, p.nome as pousada_nome,
                   p.cor_primaria, p.cor_clara, p.cor_escura, p.cor_rgb
            FROM usuarios u
            JOIN pousadas p ON u.pousada_id = p.id
            WHERE LOWER(u.email) = LOWER(?)";
    $stmt = $conn->prepare($sql);
    if ($stmt === false) {
        error_log("Erro ao preparar a consulta: " . $conn->error);
        $error = "Erro interno. Tente novamente mais tarde.";
    } else {
        $stmt->bind_param('s', $email);
        $stmt->execute();
        $stmt->store_result();

        if ($stmt->num_rows > 0) {
            $stmt->bind_result($id, $user_nome, $user_email, $hashed_password, $pousada_id, $is_admin, $pousada_logo, $pousada_nome, $cor_primaria, $cor_clara, $cor_escura, $cor_rgb);
            $stmt->fetch();

            if (password_verify($senha, $hashed_password)) {
                $_SESSION['user_id'] = $id;
                $_SESSION['user_name'] = $user_nome;
                $_SESSION['user_email'] = $user_email;
                $_SESSION['user_pousada_id'] = $pousada_id;
                $_SESSION['user_is_admin'] = $is_admin;
                $_SESSION['pousada_logo'] = $pousada_logo;
                $_SESSION['pousada_nome'] = $pousada_nome;
                $_SESSION['pousada_cores'] = [
                    'primaria' => $cor_primaria ?? '#28a745',
                    'clara' => $cor_clara ?? '#a8e6c1',
                    'escura' => $cor_escura ?? '#1e7e34',
                    'rgb' => $cor_rgb ?? '40, 167, 69'
                ];

                // CORREÇÃO: Redirecionar para index.php ao invés de index.html
                echo "<script>window.location.href = 'index.php';</script>";
                return;
            } else {
                error_log("Falha no login: senha incorreta para email '$email'");
                $error = "Email ou senha incorretos.";
            }
        } else {
            error_log("Falha no login: email não encontrado '$email'");
            $error = "Email ou senha incorretos.";
        }
        $stmt->close();
    }
}
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Login</title>
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
</head>
<body>
    <div class="container text-center mt-4">
        <img src="<?php echo $_SESSION['pousada_logo'] ?? 'img/Logo_Bom_Viver.png'; ?>" alt="Logo" class="img-fluid">
    </div>

    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 mt-5">
                <center>
                <h1>Login</h1>
                </center>
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger"><?php echo htmlspecialchars($error); ?></div>
                <?php endif; ?>
                <form method="POST">
                    <div class="mb-3">
                        <label for="email" class="form-label">Email</label>
                        <input type="email" id="email" name="email" class="form-control" required value="<?php echo htmlspecialchars($email ?? ''); ?>">
                    </div>
                    <div class="mb-3">
                        <label for="senha" class="form-label">Senha</label>
                        <input type="password" id="senha" name="senha" class="form-control" required>
                    </div>
                    <center>
                    <button type="submit" class="btn btn-primary">Login</button>
                    </center>
                </form>
                <center>
                    <br>
                    <a href="index.html" class="btn btn-secondary">Voltar</a>
                </center>
            </div>
        </div>
    </div>
    <script src="js/bootstrap.bundle.min.js"></script>
</body>
</html>
