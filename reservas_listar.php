<?php
//mostrar os erros em tempo de edição
error_reporting(E_ALL);
ini_set('display_errors', 1);

if (session_status() == PHP_SESSION_NONE) {
    session_start();
}


    if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
        $user_id = $_SESSION['user_id'];
        $pousada_id = $_SESSION['user_pousada_id'];
        $is_admin = $_SESSION['user_is_admin'];
    } else {
        echo "<script>window.location.href = 'index.html';</script>";
        return;
    }


    if (isset($_GET['hospede_id']) && isset($_GET['hospede_nome'])) {
        $hospede_id = $_GET['hospede_id'] ?? null;
        $hospede_nome = $_GET['hospede_nome'] ?? null;
    } elseif (isset($_POST['hospede_id']) && isset($_POST['hospede_nome'])) {
        $hospede_id = $_POST['hospede_id'] ?? null;
        $hospede_nome = $_POST['hospede_nome'] ?? null;
    } else {
        // Tratar o caso em que as variáveis não foram enviadas
        // Exibir uma mensagem de erro ou redirecionar o usuário
        echo "<p class='alert alert-danger'>Não foi possível exibir as reservas.</p>";
        exit;
    }


?>

<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link href="css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link rel="stylesheet" href="custom/css/form_fnrh.css">
    <title>Listagem de Reservas</title>
</head>

<body>
    <div class="container">
        <div class="text-center mt-4">
            <h1>Lista de Reservas</h1>
            <button onclick="location.href='index.php';" class='btn btn-success mb-4'>Home</button>
        </div>

        <?php
            include_once("config.php");

            function renderTable($res)
            {
                echo "<table class='table table-hover table-striped table-bordered'>";
                echo "<thead>";
                echo "<tr>";
                echo "<th>UH</th>";
                echo "<th>Entrada</th>";
                echo "<th>Saída</th>";
                echo "<th>Nº Acomp</th>";
                echo "<th>Acompanhantes</th>";
                echo "<th>Valor</th>";
                echo "<th>Ações</th>";
                echo "</tr>";
                echo "</thead>";
                echo "<tbody>";

                while ($row = $res->fetch_object()) {
                    echo "<tr>";
                    echo "<td data-label='UH'>" . $row->uh . "</td>";
                    echo "<td data-label='Entrada'>" . date('d/m/Y', strtotime($row->dataentrada)) . "</td>";
                    echo "<td data-label='Saída'>" . date('d/m/Y', strtotime($row->datasaida)) . "</td>";
                    echo "<td data-label='Nº Acomp'>" . $row->numacomp . "</td>";
                    echo "<td data-label='Acompanhantes'>" . $row->acompanhantes . "</td>";
                    echo "<td data-label='Valor'>R$ " . number_format($row->valor, 2, ',', '.') . "</td>";

                    // Substitua:
                    // <a href='index.php?page=reservas_editar&reserva_id=" . $row->reserva_id . "&hospede_nome=" . $row->hospede_nome . "' class='btn btn-sm btn-primary action-icon' title='Editar'>
                    
                    // Por:
                    echo "<td data-label='Ações' class='text-center'>
                        <button onclick='abrirModalEdicao(" . $row->reserva_id . ", \"" . addslashes($row->hospede_nome) . "\")' class='btn btn-sm btn-primary action-icon' title='Editar'><i class='bi bi-pencil-square'></i></button>
                        <a href='javascript:void(0)' onclick=\"if(confirm('Tem certeza que deseja excluir?')){location.href='index.php?page=reservas_salvar&acao=excluir&hospede_id=" . $row->hospede_id . "&reserva_id=" . $row->reserva_id . "&hospede_nome=" . $row->hospede_nome . "';};\" class='btn btn-sm btn-danger action-icon' title='Excluir'><i class='bi bi-trash'></i></a>
                        </td>";                                                                                                                                                                  
                    echo "</tr>";
                }

                echo "</tbody>";
                echo "</table>";
            }

            // Executar a query principal para listar as reservas
            $sql = "SELECT r.*, r.id as reserva_id, h.id as hospede_id, h.nome as hospede_nome
                    FROM reservas r
                    LEFT JOIN hospedes h ON r.hospede_id = h.id
                    WHERE r.pousada_id = ? AND r.hospede_id = ?
                    ORDER BY r.dataentrada DESC";

            $stmt = $conn->prepare($sql);
            $stmt->bind_param('ii', $pousada_id, $hospede_id);
            $stmt->execute();
            $res = $stmt->get_result();
            $qtd = $res->num_rows;

            echo "<center><h3><b> " . htmlspecialchars($hospede_nome) . " </b></h3></center>";

            if ($qtd > 0) {
                renderTable($res);
            } else {
                echo "<p class='alert alert-danger'>Não existem reservas cadastradas.</p>";
            }

            echo "<div class='text-center mt-4 mb-4'>
                <button onclick=\"location.href='index.php?page=reservas_novo&hospede_id=$hospede_id&hospede_nome=$hospede_nome';\" class='btn btn-primary'>Nova Reserva</button>
                <button onclick=\"location.href='index.php?page=mapa_uh';\" class='btn btn-info'>Mapa de UH</button>
                <button onclick=\"location.href='index.php';\" class='btn btn-success'>Home</button>
            </div>";

        ?>


    </div>
    
    <!-- Adicionar Bootstrap JS -->    
    <script src="js/bootstrap.bundle.min.js"></script>
</body>

</html>

<!-- Modal de Editar Reserva -->
<div class="modal fade" id="editarReservaModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Editar Reserva</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="formularioEditarContainer">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Carregando...</span>
                    </div>
                    <p>Carregando formulário...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
            </div>
        </div>
    </div>
</div>

<script src="custom/js/modal-editar-reserva.js"></script>
<script>
let modalEditarReserva;

document.addEventListener('DOMContentLoaded', function() {
    modalEditarReserva = new ModalEditarReserva();
});

function abrirModalEdicao(reservaId, hospedeNome) {
    if (modalEditarReserva) {
        modalEditarReserva.carregarFormularioEdicao(reservaId, hospedeNome);
    }
}
</script>
