<?php

ini_set('display_errors', 1);
error_reporting(E_ALL);
error_log(print_r($_POST, true));

if (session_status() == PHP_SESSION_NONE) {
    session_start(); 
}

include_once("config.php");

// DETECÇÃO DE CONTEXTO - Evita divergências!
$is_ajax = isset($_POST['ajax']) && $_POST['ajax'] == '1';

// DEBUG - adicionar logs para diagnosticar
error_log("DEBUG reservas_salvar.php:");
error_log("is_ajax: " . ($is_ajax ? 'true' : 'false'));
error_log("POST data: " . print_r($_POST, true));
error_log("REQUEST acao: " . ($_REQUEST["acao"] ?? 'NULL'));
error_log("hospede_id: " . ($hospede_id ?? 'NULL'));

// Função para resposta unificada
function enviarResposta($sucesso, $mensagem, $dados_extras = []) {
    global $is_ajax, $hospede_id, $hospede_nome;
    
    if ($is_ajax) {
        // Contexto AJAX - retornar JSON
        header('Content-Type: application/json');
        $resposta = [
            'success' => $sucesso,
            'message' => $mensagem
        ];
        $resposta = array_merge($resposta, $dados_extras);
        echo json_encode($resposta);
        exit;
    } else {
        // Contexto normal - usar alert e redirect
        if ($sucesso) {
            echo "<script>alert('$mensagem');</script>";
        } else {
            echo "<script>alert('Erro: $mensagem');</script>";
        }
        // Se não tiver hospede_id/nome, redirecionar para mapa
        if (empty($hospede_id) || empty($hospede_nome)) {
            echo "<script>location.href='index.php?page=mapa_uh';</script>";
        } else {
            echo "<script>location.href='index.php?page=reservas&hospede_id=" . urlencode($hospede_id) . "&hospede_nome=" . urlencode($hospede_nome) . "';</script>";
        }
        exit;
    }
}

// Obter dados baseado no contexto
if ($is_ajax) {
    // Contexto AJAX - dados vêm dos campos hidden específicos
    $hospede_id = $_POST['hospede_id'] ?? null;
    $hospede_nome = $_POST['hospede_nome'] ?? null;
    $reserva_id = $_POST['reserva_id'] ?? null;
} else {
    // Contexto normal - dados vêm via GET ou POST como antes
    $hospede_id = $_GET['hospede_id'] ?? $_POST['hospede_id'] ?? null;
    $hospede_nome = $_GET['hospede_nome'] ?? $_POST['hospede_nome'] ?? null;
    $reserva_id = $_GET['reserva_id'] ?? $_POST['reserva_id'] ?? null;

    // Para algumas ações (como excluir), hospede_id pode estar vazio
    $acao = $_GET['acao'] ?? $_POST['acao'] ?? null;
    if (!$hospede_id && !$hospede_nome && $acao !== 'excluir') {
        enviarResposta(false, "Não foi possível processar a reserva - dados incompletos.");
    }
}

// Verifica se o usuário está logado
if (isset($_SESSION['user_id']) && isset($_SESSION['user_pousada_id'])) {
    $user_id = $_SESSION['user_id'];
    $pousada_id = $_SESSION['user_pousada_id'];
} else {
    if ($is_ajax) {
        http_response_code(401);
        echo json_encode(['success' => false, 'message' => 'Usuário não autenticado']);
        exit;
    } else {
        echo "<script>window.location.href = 'index.html';</script>";
        return;
    }
}

// Função para criar lançamento financeiro a partir de uma reserva
function criarLancamentoFinanceiro($conn, $reserva_id, $hospede_nome, $uh, $valor, $data_entrada, $data_saida, $pousada_id, $user_id) {
    // Verificar se já existe um lançamento para esta reserva
    $sql_check = "SELECT id FROM lancamentos_financeiros WHERE reserva_id = ? AND pousada_id = ? AND tipo = 'receita'";
    $stmt_check = $conn->prepare($sql_check);
    $stmt_check->bind_param("ii", $reserva_id, $pousada_id);
    $stmt_check->execute();
    $result_check = $stmt_check->get_result();
    
    // Se já existe um lançamento, não criar outro
    if ($result_check->num_rows > 0) {
        return true;
    }
    
    // Buscar categoria padrão para hospedagem
    $sql_categoria = "SELECT id FROM categorias_financeiras 
                     WHERE pousada_id = ? AND tipo = 'receita' AND is_default = 1 
                     LIMIT 1";
    $stmt_categoria = $conn->prepare($sql_categoria);
    $stmt_categoria->bind_param("i", $pousada_id);
    $stmt_categoria->execute();
    $result_categoria = $stmt_categoria->get_result();
    
    if ($result_categoria->num_rows == 0) {
        // Se não houver categoria padrão, criar uma
        $sql_nova_categoria = "INSERT INTO categorias_financeiras 
                              (pousada_id, nome, tipo, cor, is_default) 
                              VALUES (?, 'Hospedagem', 'receita', '#28a745', 1)";
        $stmt_nova_categoria = $conn->prepare($sql_nova_categoria);
        $stmt_nova_categoria->bind_param("i", $pousada_id);
        $stmt_nova_categoria->execute();
        $categoria_id = $conn->insert_id;
    } else {
        $categoria = $result_categoria->fetch_assoc();
        $categoria_id = $categoria['id'];
    }
    
    // Criar descrição do lançamento
    $descricao = "Hospedagem - $hospede_nome - UH $uh";
    
    // Definir datas
    $data_lancamento = date('Y-m-d');
    $data_vencimento = $data_entrada; // Vencimento na data de entrada
    
    // Inserir o lançamento
    $sql_lancamento = "INSERT INTO lancamentos_financeiros 
                      (pousada_id, reserva_id, tipo, categoria_id, descricao, valor, 
                       data_lancamento, data_vencimento, status, usuario_id) 
                      VALUES (?, ?, 'receita', ?, ?, ?, ?, ?, 'pendente', ?)";
    
    $stmt_lancamento = $conn->prepare($sql_lancamento);
    $stmt_lancamento->bind_param("iiisdssi", 
                               $pousada_id, $reserva_id, $categoria_id, $descricao, $valor, 
                               $data_lancamento, $data_vencimento, $user_id);
    
    return $stmt_lancamento->execute();
}

// Função para atualizar lançamento financeiro existente
function atualizarLancamentoFinanceiro($conn, $reserva_id, $hospede_nome, $uh, $valor, $data_entrada, $pousada_id) {
    // Buscar lançamento existente
    $sql_lancamento = "SELECT id FROM lancamentos_financeiros 
                      WHERE reserva_id = ? AND pousada_id = ? AND tipo = 'receita'";
    $stmt_lancamento = $conn->prepare($sql_lancamento);
    $stmt_lancamento->bind_param("ii", $reserva_id, $pousada_id);
    $stmt_lancamento->execute();
    $result_lancamento = $stmt_lancamento->get_result();
    
    if ($result_lancamento->num_rows == 0) {
        return false;
    }
    
    $lancamento = $result_lancamento->fetch_assoc();
    $lancamento_id = $lancamento['id'];
    
    // Atualizar descrição e valor
    $descricao = "Hospedagem - $hospede_nome - UH $uh";
    
    // Não atualizar se o lançamento já estiver pago
    $sql_check_status = "SELECT status FROM lancamentos_financeiros WHERE id = ?";
    $stmt_check_status = $conn->prepare($sql_check_status);
    $stmt_check_status->bind_param("i", $lancamento_id);
    $stmt_check_status->execute();
    $result_status = $stmt_check_status->get_result();
    $status_info = $result_status->fetch_assoc();
    
    if ($status_info['status'] == 'pago') {
        // Se já estiver pago, não atualizar valor nem data de vencimento
        $sql_update = "UPDATE lancamentos_financeiros 
                      SET descricao = ? 
                      WHERE id = ?";
        $stmt_update = $conn->prepare($sql_update);
        $stmt_update->bind_param("si", $descricao, $lancamento_id);
    } else {
        // Se estiver pendente, atualizar valor e data de vencimento também
        $sql_update = "UPDATE lancamentos_financeiros 
                      SET descricao = ?, valor = ?, data_vencimento = ? 
                      WHERE id = ?";
        $stmt_update = $conn->prepare($sql_update);
        $stmt_update->bind_param("sdsi", $descricao, $valor, $data_entrada, $lancamento_id);
    }
    
    return $stmt_update->execute();
}

// Função para cancelar lançamento financeiro
function cancelarLancamentoFinanceiro($conn, $reserva_id, $pousada_id) {
    // Buscar lançamento existente
    $sql_lancamento = "SELECT id, status FROM lancamentos_financeiros 
                      WHERE reserva_id = ? AND pousada_id = ? AND tipo = 'receita'";
    $stmt_lancamento = $conn->prepare($sql_lancamento);
    $stmt_lancamento->bind_param("ii", $reserva_id, $pousada_id);
    $stmt_lancamento->execute();
    $result_lancamento = $stmt_lancamento->get_result();
    
    if ($result_lancamento->num_rows == 0) {
        return true; // Não há lançamento para cancelar
    }
    
    $lancamento = $result_lancamento->fetch_assoc();
    
    // Se o lançamento já estiver pago
    if ($lancamento['status'] == 'pago') {
        // Não podemos simplesmente cancelar, precisamos criar um estorno
        $sql_estorno = "SELECT * FROM lancamentos_financeiros WHERE id = ?";
        $stmt_estorno = $conn->prepare($sql_estorno);
        $stmt_estorno->bind_param("i", $lancamento['id']);
        $stmt_estorno->execute();
        $result_estorno = $stmt_estorno->get_result();
        $lancamento_original = $result_estorno->fetch_assoc();
        
        // Criar um lançamento de estorno
        $descricao = "Estorno - " . $lancamento_original['descricao'];
        $valor = $lancamento_original['valor'];
        $data_lancamento = date('Y-m-d');
        $data_vencimento = date('Y-m-d');
        $data_pagamento = date('Y-m-d');
        $forma_pagamento = $lancamento_original['forma_pagamento'];
        $observacao = "Estorno automático devido ao cancelamento da reserva #" . $reserva_id;
        
        $sql_insert_estorno = "INSERT INTO lancamentos_financeiros 
                              (pousada_id, reserva_id, tipo, categoria_id, descricao, valor, 
                               data_lancamento, data_vencimento, data_pagamento, status, 
                               forma_pagamento, observacao, usuario_id) 
                              VALUES (?, ?, 'despesa', ?, ?, ?, ?, ?, ?, 'pago', ?, ?, ?)";
        
        $stmt_insert_estorno = $conn->prepare($sql_insert_estorno);
        $stmt_insert_estorno->bind_param("iiisdssssi", 
                                       $pousada_id, $reserva_id, $lancamento_original['categoria_id'], 
                                       $descricao, $valor, $data_lancamento, $data_vencimento, 
                                       $data_pagamento, $forma_pagamento, $observacao, $user_id);
        
        $result_estorno = $stmt_insert_estorno->execute();
        
        // Adicionar observação ao lançamento original
        $nova_observacao = ($lancamento_original['observacao'] ? $lancamento_original['observacao'] . "\n" : "") . 
                          "Reserva cancelada em " . date('d/m/Y') . ". Estorno gerado automaticamente.";
        
        $sql_update_original = "UPDATE lancamentos_financeiros 
                               SET observacao = ? 
                               WHERE id = ?";
        $stmt_update_original = $conn->prepare($sql_update_original);
        $stmt_update_original->bind_param("si", $nova_observacao, $lancamento['id']);
        $result_update = $stmt_update_original->execute();
        
        return $result_estorno && $result_update;
    } else {
        // Se estiver pendente, apenas cancelar
        $sql_update = "UPDATE lancamentos_financeiros 
                      SET status = 'cancelado', 
                          observacao = CONCAT(IFNULL(observacao, ''), '\nCancelado automaticamente devido ao cancelamento da reserva #', ?) 
                      WHERE id = ?";
        $stmt_update = $conn->prepare($sql_update);
        $stmt_update->bind_param("ii", $reserva_id, $lancamento['id']);
        return $stmt_update->execute();
    }
}

switch ($_REQUEST["acao"]) {
    case "cadastrar":
        // Obter dados do formulário
        $uh = $_POST["uh"];
        $dataentrada = $_POST["dataentrada"];
        $horaentrada = $_POST["horaentrada"];
        $numacomp = $_POST["numacomp"];
        $datasaida = $_POST["datasaida"];
        $horasaida = $_POST["horasaida"];
        $vemde = $_POST["vemde"];
        $vaipara = $_POST["vaipara"];
        $motivo = $_POST["motivo"];
        $transporte = $_POST["transporte"];
        $acompanhantes = $_POST["acompanhantes"];
        $valor = $_POST["valor"];
        $observacao = $_POST["observacao"];
        
        // Converter valor para formato decimal
        $valor = str_replace(',', '.', $valor);
        
        // IMPLEMENTAR: Verificação de conflitos corrigida
        $sql_check = "SELECT r.id, h.nome FROM reservas r 
                      JOIN hospedes h ON r.hospede_id = h.id 
                      WHERE r.uh = ? AND r.pousada_id = ? 
                      AND NOT (
                          -- Nova reserva termina antes da existente começar
                          (? < r.dataentrada OR (? = r.dataentrada AND ? <= r.horaentrada))
                          OR
                          -- Nova reserva começa depois da existente terminar  
                          (? > r.datasaida OR (? = r.datasaida AND ? >= r.horasaida))
                      )
                      AND r.id != ?";
        
        $stmt_check = $conn->prepare($sql_check);
        $reserva_id_zero = 0;
        $stmt_check->bind_param("sissssssi", 
            $uh, 
            $pousada_id, 
            $datasaida,     // data saída nova reserva
            $datasaida,     // data saída nova reserva (comparação hora)
            $horasaida,     // hora saída nova reserva
            $dataentrada,   // data entrada nova reserva
            $dataentrada,   // data entrada nova reserva (comparação hora)
            $horaentrada,   // hora entrada nova reserva
            $reserva_id_zero
        );
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();
        
        if ($result_check->num_rows > 0) {
            $conflito = $result_check->fetch_assoc();
            enviarResposta(false, "UH {$uh} já reservada para {$conflito['nome']}.");
        }
        
        // Validações básicas
        if (empty($uh) || empty($dataentrada) || empty($datasaida)) {
            enviarResposta(false, "Campos obrigatórios não preenchidos (UH, datas).");
        }
        
        $sql = "INSERT INTO reservas (hospede_id, pousada_id, uh, numacomp, dataentrada, horaentrada, datasaida, horasaida, vemde, vaipara, motivo, transporte, acompanhantes, valor, observacao) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            enviarResposta(false, "Erro ao preparar consulta: " . $conn->error);
        }
        
        $stmt->bind_param("iisssssssssssds", 
            $hospede_id, 
            $pousada_id,
            $uh, 
            $numacomp, 
            $dataentrada, 
            $horaentrada, 
            $datasaida, 
            $horasaida, 
            $vemde, 
            $vaipara, 
            $motivo, 
            $transporte, 
            $acompanhantes,
            $valor,
            $observacao
        );
        
        if ($stmt->execute()) {
            $reserva_id = $conn->insert_id;
            
            // Criar lançamento financeiro para a nova reserva
            criarLancamentoFinanceiro($conn, $reserva_id, $hospede_nome, $uh, $valor, $dataentrada, $datasaida, $pousada_id, $user_id);
            
            $dados_extras = [
                'reserva_id' => $reserva_id,
                'uh' => $uh,
                'data_entrada' => $dataentrada
            ];
            
            enviarResposta(true, "Reserva cadastrada com sucesso!", $dados_extras);
        } else {
            enviarResposta(false, "Não foi possível cadastrar a reserva: " . $stmt->error);
        }
        
        $stmt->close();
        break;
        
    case "editar":
        // IMPLEMENTAR: Descomentar e corrigir a validação para edição
        $uh = $_POST["uh"];
        $numacomp = $_POST["numacomp"];
        $dataentrada = $_POST["dataentrada"];
        $horaentrada = $_POST["horaentrada"];
        $datasaida = $_POST["datasaida"];
        $horasaida = $_POST["horasaida"];
        $vemde = $_POST["vemde"];
        $vaipara = $_POST["vaipara"];
        $motivo = $_POST["motivo"];
        $transporte = $_POST["transporte"];
        $acompanhantes = $_POST["acompanhantes"];
        $valor = $_POST["valor"];
        $observacao = $_POST["observacao"];
        
        // Converter valor para formato decimal
        $valor = str_replace(',', '.', $valor);
        
        // IMPLEMENTAR: Verificação de conflitos excluindo a reserva atual
        $sql_check = "SELECT r.id, h.nome FROM reservas r 
                      JOIN hospedes h ON r.hospede_id = h.id 
                      WHERE r.uh = ? AND r.pousada_id = ? 
                      AND NOT (
                          -- Nova reserva termina antes da existente começar
                          (? < r.dataentrada OR (? = r.dataentrada AND ? <= r.horaentrada))
                          OR
                          -- Nova reserva começa depois da existente terminar  
                          (? > r.datasaida OR (? = r.datasaida AND ? >= r.horasaida))
                      )
                      AND r.id != ?";
        
        $stmt_check = $conn->prepare($sql_check);
        $stmt_check->bind_param("sissssssi", 
            $uh, 
            $pousada_id, 
            $datasaida,     // data saída nova reserva
            $datasaida,     // data saída nova reserva (comparação hora)
            $horasaida,     // hora saída nova reserva
            $dataentrada,   // data entrada nova reserva
            $dataentrada,   // data entrada nova reserva (comparação hora)
            $horaentrada,   // hora entrada nova reserva
            $reserva_id
        );
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();
        
        if ($result_check->num_rows > 0) {
            $conflito = $result_check->fetch_assoc();
            enviarResposta(false, "Conflito de reserva detectado! A UH {$uh} já está reservada para {$conflito['nome']} no período selecionado.");
        }
        
        // Executar a atualização da reserva
        $sql = "UPDATE reservas SET 
                uh=?, 
                numacomp=?, 
                dataentrada=?, 
                horaentrada=?, 
                datasaida=?, 
                horasaida=?, 
                vemde=?, 
                vaipara=?, 
                motivo=?, 
                transporte=?, 
                acompanhantes=?, 
                valor=?, 
                observacao=?
            WHERE id=? AND pousada_id=?";
        
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("sssssssssssdsii", 
            $uh, $numacomp, $dataentrada, $horaentrada, 
            $datasaida, $horasaida, $vemde, $vaipara, 
            $motivo, $transporte, $acompanhantes, $valor, $observacao,
            $reserva_id, $pousada_id
        );
        
        if ($stmt->execute()) {
            // Atualizar lançamento financeiro se existir
            atualizarLancamentoFinanceiro($conn, $reserva_id, $hospede_nome, $uh, $valor, $dataentrada, $pousada_id);
            
            enviarResposta(true, "Reserva alterada com sucesso!");
        } else {
            enviarResposta(false, "Não foi possível alterar a reserva: " . $stmt->error);
        }
        
        $stmt->close();
        break;

    case 'excluir':
        // Verificar se a reserva pertence à pousada do usuário
        $sql_check = "SELECT id FROM reservas WHERE id=? AND pousada_id=?";
        $stmt_check = $conn->prepare($sql_check);
        $stmt_check->bind_param("ii", $reserva_id, $pousada_id);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();
        
        if ($result_check->num_rows > 0) {
            // Cancelar lançamento financeiro antes de excluir
            cancelarLancamentoFinanceiro($conn, $reserva_id, $pousada_id);
            
            // Excluir a reserva
            $sql = "DELETE FROM reservas WHERE id=? AND pousada_id=?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ii", $reserva_id, $pousada_id);

            if ($stmt->execute()) {
                enviarResposta(true, "Reserva excluída com sucesso!");
            } else {
                enviarResposta(false, "Não foi possível excluir a reserva: " . $stmt->error);
            }
            $stmt->close();
        } else {
            enviarResposta(false, "Reserva não encontrada ou sem permissão para excluir.");
        }
        
        $stmt_check->close();
        break;

    case 'excluir_mapa':
        $nome = $_GET["hospede_nome"];
        $hospede_id = $_GET["hospede_id"];
        $reserva_id = $_GET["reserva_id"];

        // Verificar se a reserva pertence à pousada do usuário
        $sql_check = "SELECT id FROM reservas WHERE id=? AND pousada_id=?";
        $stmt_check = $conn->prepare($sql_check);
        $stmt_check->bind_param("ii", $reserva_id, $pousada_id);
        $stmt_check->execute();
        $result_check = $stmt_check->get_result();
        
        if ($result_check->num_rows > 0) {
            // Delete the reservation
            $sql = "DELETE FROM reservas WHERE id=? AND pousada_id=?";
            $stmt = $conn->prepare($sql);
            $stmt->bind_param("ii", $reserva_id, $pousada_id);

            if ($stmt->execute()) {
                echo "<script>alert('Reserva excluída com sucesso');</script>";
            } else {
                echo "<script>alert('Não foi possível excluir a reserva.');</script>";
            }
            $stmt->close();
        } else {
            echo "<script>alert('Reserva não encontrada ou sem permissão para excluir.');</script>";
        }
        
        $stmt_check->close();

        // Redirecionar de volta para o mapa_uh
        print "<script>location.href='index.php?page=mapa_uh';</script>";
        break;
}
?>