<?php
// Caminho para o arquivo CSS
$arquivo_css = 'este.css';

// Função para ler as cores atuais do CSS
function lerCoresCSS($arquivo) {
    if (!file_exists($arquivo)) {
        return ['background' => '#8e7eec', 'navbar' => '#04b5ea'];
    }
    
    $conteudo = file_get_contents($arquivo);
    $cores = [];
    
    // Extrair cor de fundo do body
    if (preg_match('/body\s*{[^}]*background-color:\s*([^;]+);/i', $conteudo, $matches)) {
        $cores['background'] = trim($matches[1]);
    }
    
    // Extrair cor da navbar
    if (preg_match('/\.navbar\.navbar-expand-lg\.navbar-light\s*{[^}]*background-color:\s*([^!]+)!/i', $conteudo, $matches)) {
        $cores['navbar'] = trim($matches[1]);
    }
    
    return $cores;
}

// Função para salvar as novas cores no CSS
function salvarCoresCSS($arquivo, $cor_background, $cor_navbar) {
    $css_content = "body {\n";
    $css_content .= "\tbackground-color: $cor_background;\n";
    $css_content .= "\tfont-family: Arial, sans-serif;\n";
    $css_content .= "}\n\n";
    $css_content .= ".navbar.navbar-expand-lg.navbar-light {\n";
    $css_content .= "\tbackground-color: $cor_navbar !important;\n";
    $css_content .= "}\n";
    
    return file_put_contents($arquivo, $css_content) !== false;
}

// Processar ações
if (isset($_POST['salvar_cores'])) {
    $nova_cor_background = $_POST['cor_background'];
    $nova_cor_navbar = $_POST['cor_navbar'];
    
    // Salvar novas cores
    if (salvarCoresCSS($arquivo_css, $nova_cor_background, $nova_cor_navbar)) {
        // Redirecionar para evitar resubmissão
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    }
}

// Ler cores atuais
$cores_atuais = lerCoresCSS($arquivo_css);
?>

<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Editor de Cores CSS</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            font-size: 14px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
            font-size: 16px;
        }
        
        input[type="color"] {
            width: 50px;
            height: 40px;
            border: 2px solid #ddd;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 15px;
        }
        
        .color-info {
            display: inline-block;
            padding: 10px 15px;
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            font-family: monospace;
            font-size: 14px;
            color: #495057;
        }
        
        .button-group {
            margin-top: 30px;
        }
        
        button {
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 15px;
            font-weight: 500;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-warning {
            background-color: #ffc107;
            color: #212529;
        }
        
        .btn-warning:hover {
            background-color: #e0a800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h2>Editor de Cores CSS</h2>
        
        <form method="POST">
            <div class="form-group">
                <label for="cor_background">Cor de Fundo do Body:</label>
                <input type="color" name="cor_background" id="cor_background" 
                       value="<?php echo $cores_atuais['background']; ?>">
                <span class="color-info"><?php echo $cores_atuais['background']; ?></span>
            </div>
            
            <div class="form-group">
                <label for="cor_navbar">Cor da Navbar:</label>
                <input type="color" name="cor_navbar" id="cor_navbar" 
                       value="<?php echo $cores_atuais['navbar']; ?>">
                <span class="color-info"><?php echo $cores_atuais['navbar']; ?></span>
            </div>
            
            <div class="button-group">
                <button type="submit" name="salvar_cores" class="btn-primary">💾 Salvar Cores no CSS</button>
            </div>
        </form>
    </div>
</body>
</html>